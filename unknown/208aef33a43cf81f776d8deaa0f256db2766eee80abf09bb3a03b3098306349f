/**
 * Document Domain Security Configuration
 */

import DOM<PERSON>IN_CONFIG from "../config/domain";

export const DOCUMENT_DOMAIN = DOMAIN_CONFIG.id;
export const DOCUMENT_PERMISSIONS = DOMAIN_CONFIG.permissions;
export const DOCUMENT_ROUTE_PERMISSIONS = DOMAIN_CONFIG.routePermissions;

const DOCUMENT_SECURITY = {
  domain: DOCUMENT_DOMAIN,
  permissions: DOCUMENT_PERMISSIONS,
  routePermissions: DOCUMENT_ROUTE_PERMISSIONS,
};

export default DOCUMENT_SECURITY;
