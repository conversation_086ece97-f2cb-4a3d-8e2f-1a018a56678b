import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileX, ArrowLeft } from "lucide-react";
import Link from "next/link";

/**
 * Not found page for Document Attachments
 */
export default function AttachmentsNotFound() {
  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <FileX className="h-12 w-12 text-muted-foreground" />
        </div>
        <CardTitle>Page Not Found</CardTitle>
        <CardDescription>
          The document attachments page you're looking for doesn't exist.
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <Button asChild>
          <Link href="/protected/document/attachments">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Attachments
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
