import { notFound } from "next/navigation";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachmentById } from "../../actions/attachment-actions";
import { UserManagementService } from "@/app/[lang]/protected/user/lib/services/UserManagementService";
import { AttachmentActions } from "../components/AttachmentActions";
import { DocumentViewer } from "../components/DocumentViewer";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "@/components/typography";
import { DetailPageHeader } from "@/components/DetailPageHeader";
import {
  HistoryTimeline,
  type HistoryItem,
} from "@/components/ui/history-timeline/history-timeline";

interface AttachmentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Document Attachment View Page
 * Displays attachment details with preview and download options
 */
export default async function AttachmentViewPage({ params }: AttachmentViewPageProps) {
  // Await the params
  const { lang, id } = await params;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters (returnUrl removed since back button was removed)

  // Call server action to get attachment details
  const result = await getAttachmentById(id);
  if (!result.success || !result.data) {
    notFound();
  }

  const attachment = result.data;

  // Get user information for the uploader
  let uploaderName = attachment.uploaded_by;
  try {
    const userProfile = await UserManagementService.getUserProfile(attachment.uploaded_by);
    if (userProfile) {
      uploaderName = `${userProfile.firstName} ${userProfile.lastName}`.trim() || userProfile.email;
    }
  } catch (error) {
    // If we can't get user info, just use the UUID
    console.warn("Could not fetch user profile for uploader:", error);
  }

  // Extract metadata values
  const documentLanguage = attachment.language || "";
  // Extract metadata values
  const metadata = (attachment.metadata as any) || {};
  const documentTags = Array.isArray(metadata.tags) ? metadata.tags.join(", ") : "";
  const documentCategory = metadata.category || "";

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Create history timeline data
  const historyItems: HistoryItem[] = [
    {
      id: "upload",
      timestamp: attachment.uploaded_at || new Date().toISOString(),
      user: {
        id: attachment.uploaded_by || "unknown",
        name: uploaderName,
      },
      action: "create",
      summary: `Document uploaded: ${attachment.document_name} (${formatFileSize(attachment.file_size || 0)})`,
    },
  ];

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-8">
        {/* Header Section - Using DetailPageHeader Component */}
        <DetailPageHeader
          className="mt-6 mb-8"
          title="Document"
          metadata={[
            {
              value: <Badge variant="default">{attachment.document_type}</Badge>,
            },
            {
              value: <Badge variant="secondary">{documentCategory}</Badge>,
            },
            {
              value: <Badge variant="secondary">{documentTags}</Badge>,
            },
          ]}
          actions={
            <>
              <span className="hidden sm:block">
                <AttachmentActions
                  attachmentId={id}
                  // attachedToType={attachment.attached_to_type}
                  // attachedToId={attachment.attached_to_id}
                  dictionary={dictionary}
                  variant="compact"
                />
              </span>
              <span className="ml-3">
                <Button asChild>
                  <Link href={`/api/documents/${id}/download`}>
                    <Download className="mr-1.5 -ml-0.5 h-5 w-5" />
                    {dictionary.documents?.detail?.download || "Download"}
                  </Link>
                </Button>
              </span>
            </>
          }
        />

        {/* Main Content - 70/30 Split */}
        <div className="grid grid-cols-1 lg:grid-cols-10 gap-8 mt-8">
          {/* Document Preview - 70% */}
          <div className="lg:col-span-7">
            <Card>
              <CardContent className="p-6">
                <div className="rounded-lg aspect-[8.5/11] overflow-hidden border">
                  <DocumentViewer
                    documentId={id}
                    documentName={attachment.document_name}
                    documentType={attachment.document_type}
                    dictionary={dictionary}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - 30% */}
          <div className="lg:col-span-3 space-y-8">
            {/* Document Info Card */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {dictionary.documents?.detail?.documentInfo || "Document Info"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-0 divide-y divide-gray-200">
                  {/* File Name */}
                  <div className="flex items-center justify-between py-3">
                    <div className="flex items-center space-x-3">
                      <Small>{dictionary.documents?.detail?.fileName || "File Name"}</Small>
                    </div>
                    <Tiny>{attachment.document_name}</Tiny>
                  </div>

                  {/* File Size */}
                  <div className="flex items-center justify-between py-3">
                    <div className="flex items-center space-x-3">
                      <Small>{dictionary.documents?.detail?.fileSize || "File Size"}</Small>
                    </div>
                    <Tiny>{formatFileSize(attachment.file_size || 0)}</Tiny>
                  </div>

                  {/* Lannugae */}
                  <div className="flex items-center justify-between py-3">
                    <div className="flex items-center space-x-3">
                      <Small>{dictionary.documents?.detail?.language || "Uploaded By"}</Small>
                    </div>
                    <Tiny>{documentLanguage}</Tiny>
                  </div>

                  {/* Related Entity Link */}
                  <div className="pt-4 border-t">
                    <AttachmentActions
                      attachmentId={id}
                      attachedToType={attachment.attached_to_type}
                      attachedToId={attachment.attached_to_id}
                      dictionary={dictionary}
                      variant="inline"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Document History Timeline */}
            <HistoryTimeline
              title={dictionary.documents?.detail?.timeline || "Timeline"}
              items={historyItems}
            />
          </div>
        </div>
      </div>
    </AuthorizationRequired>
  );
}
