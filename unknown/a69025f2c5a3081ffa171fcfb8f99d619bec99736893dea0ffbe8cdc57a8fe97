import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { History, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AssignmentHistoryNotFound() {
  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <History className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle>History Not Found</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            No assignment history found or the history page is not available.
          </p>
          <Button asChild className="w-full">
            <Link href="/protected/scheduling/assignments">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Assignments
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
