"use client";

import { useEffect, useState } from "react";
import { DocumentAttachment, Document } from "@/components/ui/document-attachment";
import {
  getEntityAttachments,
  deleteAttachment,
} from "@/app/[lang]/protected/document/(features)/attachments/actions/attachment-actions";
import { toast } from "sonner";
import { Database } from "@/lib/types/database.types";

type DocumentAttachmentRow = Database["public"]["Tables"]["document_attachments"]["Row"];

interface DocumentListProps {
  entityType: string;
  entityId: string;
  title?: string;
  description?: string;
  allowUpload?: boolean;
  allowDelete?: boolean;
  className?: string;
}

/**
 * Polymorphic DocumentList component that follows the document system specification
 *
 * @example
 * ```tsx
 * <DocumentList entityType="case_file" entityId={caseFileId} />
 * <DocumentList entityType="contact" entityId={contactId} />
 * ```
 */
export function DocumentList({
  entityType,
  entityId,
  title = "Documents",
  description,
  allowUpload = true,
  allowDelete = true,
  className = "",
}: DocumentListProps) {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);

  // Convert database row to Document interface
  const convertToDocument = (attachment: DocumentAttachmentRow): Document => ({
    id: attachment.id,
    name: attachment.document_name,
    type: attachment.document_type,
    size: attachment.file_size || 0,
    uploadedAt: attachment.uploaded_at || new Date().toISOString(),
    uploadedBy: "Unknown", // TODO: Add user info to attachments
    url: `/api/documents/${attachment.id}/download`,
  });

  // Load documents for the entity
  const loadDocuments = async () => {
    try {
      setLoading(true);
      const result = await getEntityAttachments(entityType, entityId);

      if (result.success && result.data) {
        const convertedDocuments = result.data.map(convertToDocument);
        setDocuments(convertedDocuments);
      } else {
        console.error("Failed to load documents:", result.error);
        toast.error("Failed to load documents");
      }
    } catch (error) {
      console.error("Error loading documents:", error);
      toast.error("Failed to load documents");
    } finally {
      setLoading(false);
    }
  };

  // Load documents on mount and when entity changes
  useEffect(() => {
    if (entityType && entityId) {
      loadDocuments();
    }
  }, [entityType, entityId]);

  // Handle file upload
  const handleUpload = async (files: File[]) => {
    try {
      for (const file of files) {
        const formData = new FormData();
        formData.append("files", file);
        formData.append("attached_to_type", entityType);
        formData.append("attached_to_id", entityId);
        formData.append("name", file.name);

        const response = await fetch("/api/documents/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} document(s)`);
      await loadDocuments(); // Reload documents
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents");
    }
  };

  // Handle document deletion
  const handleDelete = async (documentId: string) => {
    try {
      const result = await deleteAttachment(documentId);

      if (result.success) {
        toast.success("Document deleted successfully");
        await loadDocuments(); // Reload documents
      } else {
        toast.error(result.error || "Failed to delete document");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("Failed to delete document");
    }
  };

  // Handle document viewing - use the proper document viewer page
  const handleView = (document: Document) => {
    window.open(`/fr/protected/document/attachments/${document.id}/view`, "_blank");
  };

  // Handle document download
  const handleDownload = (document: Document) => {
    window.open(document.url, "_blank");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading documents...</div>
      </div>
    );
  }

  return (
    <DocumentAttachment
      title={title}
      description={description}
      documents={documents}
      onUpload={allowUpload ? handleUpload : undefined}
      onDelete={allowDelete ? handleDelete : undefined}
      onView={handleView}
      onDownload={handleDownload}
      allowUpload={allowUpload}
      allowDelete={allowDelete}
      className={className}
    />
  );
}
