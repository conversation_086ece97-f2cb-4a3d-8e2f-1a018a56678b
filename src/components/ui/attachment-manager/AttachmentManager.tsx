"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { FileText, Download, Eye, Upload } from "lucide-react";

export interface AttachmentItem {
  id: string;
  document_name: string;
  document_type: string;
  file_size: number;
  attached_to_type: string;
  attached_to_id: string;
  uploaded_at: string;
  uploaded_by: string;
  metadata?: {
    category?: string;
    tags?: string[];
    description?: string;
  };
}

interface AttachmentManagerProps {
  attachments: AttachmentItem[];
  onView?: (attachment: AttachmentItem) => void;
  onDownload?: (attachment: AttachmentItem) => void;
  onUpload?: () => void;
  loading?: boolean;
  title?: string;
  description?: string;
}

export function AttachmentManager({
  attachments,
  onView,
  onDownload,
  onUpload,
  loading = false,
  title = "Attachments",
  description,
}: AttachmentManagerProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">Loading attachments...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (attachments.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">No attachments found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Upload your first document to get started
            </p>
            {onUpload && (
              <Button onClick={onUpload}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {attachments.map((attachment) => (
          <Card key={attachment.id}>
            <CardHeader>
              <CardTitle>{attachment.document_name}</CardTitle>
              <CardDescription> {attachment?.metadata?.description}</CardDescription>
            </CardHeader>

            <CardContent>
              <Separator />
            </CardContent>

            <CardFooter>
              {onDownload && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-8 text-xs"
                  onClick={() => onDownload(attachment)}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              )}

              {onView && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 h-8 text-xs"
                  onClick={() => onView(attachment)}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Details
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
}
