"use client";

import React, { useState, useRef, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Upload, FileText, X, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export interface FileUploadItem {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress: number;
  error?: string;
}

interface FileUploaderProps {
  onFilesSelected?: (files: File[]) => void;
  onUpload?: (files: FileUploadItem[]) => Promise<void>;
  onRemoveFile?: (fileId: string) => void;
  maxFiles?: number;
  maxFileSize?: number;
  allowedTypes?: string[];
  multiple?: boolean;
  disabled?: boolean;
  accept?: string;
  className?: string;
  uploadText?: string;
  browseText?: string;
  showFileList?: boolean;
}

export function FileUploader({
  onFilesSelected,
  onUpload,
  onRemoveFile,
  maxFiles = 10,
  maxFileSize = 50 * 1024 * 1024,
  allowedTypes = ["application/pdf", "image/jpeg", "image/png"],
  multiple = true,
  disabled = false,
  accept,
  className,
  uploadText = "Drop files here or click to browse",
  browseText = "Choose Files",
  showFileList = true,
}: FileUploaderProps) {
  const [files, setFiles] = useState<FileUploadItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize) {
      return `File size exceeds ${formatFileSize(maxFileSize)}`;
    }
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return "File type not allowed";
    }
    return null;
  };

  const handleFiles = useCallback(
    (selectedFiles: FileList | File[]) => {
      const fileArray = Array.from(selectedFiles);
      const newFiles: FileUploadItem[] = [];

      if (files.length + fileArray.length > maxFiles) {
        return;
      }

      fileArray.forEach((file) => {
        const error = validateFile(file);
        if (!error) {
          newFiles.push({
            file,
            id: `${Date.now()}-${Math.random().toString(36).substring(7)}`,
            status: "pending",
            progress: 0,
          });
        }
      });

      const updatedFiles = [...files, ...newFiles];
      setFiles(updatedFiles);
      onFilesSelected?.(newFiles.map((f) => f.file));
    },
    [files, maxFiles, onFilesSelected]
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);

      if (disabled) return;

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles.length > 0) {
        handleFiles(droppedFiles);
      }
    },
    [disabled, handleFiles]
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        handleFiles(selectedFiles);
      }
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [handleFiles]
  );

  const removeFile = useCallback(
    (fileId: string) => {
      setFiles((prev) => prev.filter((f) => f.id !== fileId));
      onRemoveFile?.(fileId);
    },
    [onRemoveFile]
  );

  const handleUpload = useCallback(async () => {
    if (files.length === 0 || isUploading) return;

    setIsUploading(true);
    try {
      await onUpload?.(files);
    } catch (error) {
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
    }
  }, [files, isUploading, onUpload]);

  return (
    <div className={cn("space-y-4", className)}>
      <Card
        className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragOver && !disabled && "border-primary bg-primary/5",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <Upload
            className={cn(
              "h-12 w-12 mb-4",
              isDragOver && !disabled ? "text-primary" : "text-muted-foreground"
            )}
          />
          <h3 className="text-lg font-medium mb-2">{uploadText}</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {multiple ? `Up to ${maxFiles} files` : "Single file"} • Max{" "}
            {formatFileSize(maxFileSize)} each
          </p>
          <Button variant="outline" disabled={disabled}>
            <Upload className="h-4 w-4 mr-2" />
            {browseText}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple={multiple}
            accept={accept || allowedTypes.join(",")}
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />
        </CardContent>
      </Card>

      {showFileList && files.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Selected Files ({files.length})</h4>
                {files.length > 0 && !isUploading && (
                  <Button onClick={handleUpload} size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload All
                  </Button>
                )}
              </div>

              <div className="space-y-2">
                {files.map((fileItem) => (
                  <div key={fileItem.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <FileText className="h-4 w-4 text-gray-500" />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium truncate">{fileItem.file.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {formatFileSize(fileItem.file.size)}
                        </Badge>
                      </div>

                      {fileItem.status === "uploading" && (
                        <Progress value={fileItem.progress} className="mt-1 h-1" />
                      )}

                      {fileItem.error && (
                        <p className="text-xs text-red-500 mt-1">{fileItem.error}</p>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {fileItem.status === "uploading" && (
                        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                      )}
                      {fileItem.status === "success" && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {fileItem.status === "error" && (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}

                      {fileItem.status === "pending" && (
                        <Button variant="ghost" size="sm" onClick={() => removeFile(fileItem.id)}>
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
