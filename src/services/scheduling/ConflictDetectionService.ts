import { createClient } from "@/lib/supabase/server";
import { Database } from "@/lib/types/database.types";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";

// Type definitions from database schema
type AppointmentAssignment = Database["public"]["Tables"]["appointment_assignments"]["Row"];
type Appointment = Database["public"]["Tables"]["appointments"]["Row"];
type Employee = Database["public"]["Tables"]["employees"]["Row"];
type EmployeeAvailability = Database["public"]["Tables"]["employee_availability"]["Row"];

// Conflict detection types
export interface AssignmentConflict {
  id: string;
  type: "double_booking" | "availability_conflict" | "qualification_mismatch" | "workload_exceeded";
  severity: "low" | "medium" | "high" | "critical";
  employeeId: string;
  employeeName: string;
  appointmentId: string;
  appointmentTitle: string;
  conflictingAppointmentId?: string;
  conflictingAppointmentTitle?: string;
  conflictDate: string;
  conflictTime: string;
  description: string;
  suggestedResolutions: ConflictResolution[];
  detectedAt: string;
}

export interface ConflictResolution {
  type:
    | "reassign_employee"
    | "reschedule_appointment"
    | "split_assignment"
    | "add_secondary_employee";
  description: string;
  feasible: boolean;
  alternativeEmployees?: Array<{
    employeeId: string;
    employeeName: string;
    availability: boolean;
  }>;
  alternativeTimeSlots?: Array<{
    date: string;
    startTime: string;
    endTime: string;
    available: boolean;
  }>;
}

export interface AvailabilityCheck {
  employeeId: string;
  appointmentId: string;
  appointmentDate: string;
  appointmentStartTime: string;
  appointmentEndTime: string;
  isAvailable: boolean;
  conflicts: string[];
  workloadStatus: "normal" | "busy" | "overloaded";
}

export interface ConflictSummary {
  totalConflicts: number;
  criticalConflicts: number;
  highPriorityConflicts: number;
  mediumPriorityConflicts: number;
  lowPriorityConflicts: number;
  conflictsByType: Record<string, number>;
  resolvedToday: number;
}

/**
 * ConflictDetectionService - Server-side service for detecting and resolving assignment conflicts
 * Provides real-time conflict detection and resolution suggestions
 */
export class ConflictDetectionService {
  private async getSupabase() {
    return createClient();
  }

  /**
   * Check for assignment conflicts for a specific employee
   */
  async checkEmployeeAvailability(
    employeeId: string,
    appointmentId: string,
    organizationId: string
  ): Promise<ServiceResponse<AvailabilityCheck>> {
    try {
      const supabase = await this.getSupabase();

      // Get the appointment details
      const { data: appointment, error: appointmentError } = await supabase
        .from("appointments")
        .select("*")
        .eq("id", appointmentId)
        .eq("organization_id", organizationId)
        .single();

      if (appointmentError || !appointment) {
        return errorResponse("Appointment not found", "Appointment not found");
      }

      // Check for conflicting assignments
      const { data: conflictingAssignments, error: conflictError } = await supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*)
        `
        )
        .eq("employee_id", employeeId)
        .eq("organization_id", organizationId)
        .neq("appointment_id", appointmentId);

      if (conflictError) {
        return errorResponse(conflictError, `Failed to check conflicts: ${conflictError.message}`);
      }

      // Check for time overlaps
      const conflicts: string[] = [];
      let workloadCount = 0;

      if (conflictingAssignments) {
        for (const assignment of conflictingAssignments) {
          const conflictingAppointment = assignment.appointments as Appointment;

          if (conflictingAppointment.appointment_date === appointment.appointment_date) {
            // Check for time overlap
            const appointmentStart = new Date(
              `${appointment.appointment_date}T${appointment.start_time}`
            );
            const appointmentEnd = new Date(
              `${appointment.appointment_date}T${appointment.end_time}`
            );
            const conflictStart = new Date(
              `${conflictingAppointment.appointment_date}T${conflictingAppointment.start_time}`
            );
            const conflictEnd = new Date(
              `${conflictingAppointment.appointment_date}T${conflictingAppointment.end_time}`
            );

            if (
              (appointmentStart < conflictEnd && appointmentEnd > conflictStart) ||
              (conflictStart < appointmentEnd && conflictEnd > appointmentStart)
            ) {
              conflicts.push(
                `Time overlap with "${conflictingAppointment.title}" from ${conflictingAppointment.start_time} to ${conflictingAppointment.end_time}`
              );
            }
          }

          // Count workload for the same date
          if (conflictingAppointment.appointment_date === appointment.appointment_date) {
            workloadCount++;
          }
        }
      }

      // Determine workload status
      let workloadStatus: "normal" | "busy" | "overloaded" = "normal";
      if (workloadCount >= 6) {
        workloadStatus = "overloaded";
      } else if (workloadCount >= 4) {
        workloadStatus = "busy";
      }

      const availabilityCheck: AvailabilityCheck = {
        employeeId,
        appointmentId,
        appointmentDate: appointment.appointment_date,
        appointmentStartTime: appointment.start_time,
        appointmentEndTime: appointment.end_time,
        isAvailable: conflicts.length === 0,
        conflicts,
        workloadStatus,
      };

      return successResponse(availabilityCheck, "Availability check completed");
    } catch (error) {
      return errorResponse(error, "Availability check failed");
    }
  }

  /**
   * Detect all assignment conflicts for an organization
   */
  async detectAssignmentConflicts(
    organizationId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ServiceResponse<AssignmentConflict[]>> {
    try {
      const supabase = await this.getSupabase();

      // Get all assignments with appointment and employee details
      const query = supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*),
          employees!appointment_assignments_employee_id_fkey(*)
        `
        )
        .eq("organization_id", organizationId);

      const { data: assignments, error } = await query;

      if (error) {
        return errorResponse(error, `Failed to fetch assignments: ${error.message}`);
      }

      const conflicts: AssignmentConflict[] = [];

      if (assignments) {
        // Group assignments by employee and date
        const employeeAssignments = new Map<string, any[]>();

        for (const assignment of assignments) {
          const appointment = assignment.appointments as Appointment;
          const employee = assignment.employees as Employee;

          if (!appointment || !employee) continue;

          const key = `${assignment.employee_id}-${appointment.appointment_date}`;
          if (!employeeAssignments.has(key)) {
            employeeAssignments.set(key, []);
          }
          const assignments = employeeAssignments.get(key);
          if (assignments) {
            assignments.push({
              assignment,
              appointment,
              employee,
            });
          }
        }

        // Check for conflicts within each employee's daily schedule
        for (const [key, dayAssignments] of employeeAssignments) {
          if (dayAssignments.length > 1) {
            // Sort by start time
            dayAssignments.sort((a, b) =>
              a.appointment.start_time.localeCompare(b.appointment.start_time)
            );

            // Check for overlaps
            for (let i = 0; i < dayAssignments.length - 1; i++) {
              const current = dayAssignments[i];
              const next = dayAssignments[i + 1];

              const currentEnd = new Date(
                `${current.appointment.appointment_date}T${current.appointment.end_time}`
              );
              const nextStart = new Date(
                `${next.appointment.appointment_date}T${next.appointment.start_time}`
              );

              if (currentEnd > nextStart) {
                // Conflict detected
                const conflict: AssignmentConflict = {
                  id: `conflict-${current.assignment.id}-${next.assignment.id}`,
                  type: "double_booking",
                  severity: "high",
                  employeeId: current.employee.id,
                  employeeName: `${current.employee.first_name} ${current.employee.last_name}`,
                  appointmentId: current.appointment.id,
                  appointmentTitle: current.appointment.title,
                  conflictingAppointmentId: next.appointment.id,
                  conflictingAppointmentTitle: next.appointment.title,
                  conflictDate: current.appointment.appointment_date,
                  conflictTime: `${current.appointment.start_time} - ${next.appointment.end_time}`,
                  description: `Double booking: "${current.appointment.title}" overlaps with "${next.appointment.title}"`,
                  suggestedResolutions: await this.generateResolutions(current, next),
                  detectedAt: new Date().toISOString(),
                };

                conflicts.push(conflict);
              }
            }

            // Check for workload conflicts
            if (dayAssignments.length > 6) {
              const employee = dayAssignments[0].employee;
              const conflict: AssignmentConflict = {
                id: `workload-${employee.id}-${dayAssignments[0].appointment.appointment_date}`,
                type: "workload_exceeded",
                severity: "medium",
                employeeId: employee.id,
                employeeName: `${employee.first_name} ${employee.last_name}`,
                appointmentId: dayAssignments[0].appointment.id,
                appointmentTitle: "Multiple appointments",
                conflictDate: dayAssignments[0].appointment.appointment_date,
                conflictTime: "Full day",
                description: `Workload exceeded: ${dayAssignments.length} appointments scheduled for one day`,
                suggestedResolutions: [],
                detectedAt: new Date().toISOString(),
              };

              conflicts.push(conflict);
            }
          }
        }
      }

      return successResponse(conflicts, "Conflict detection completed");
    } catch (error) {
      return errorResponse(error, "Conflict detection failed");
    }
  }

  /**
   * Get available employees for a specific time slot
   */
  async getAvailableEmployees(
    appointmentDate: string,
    startTime: string,
    endTime: string,
    organizationId: string,
    excludeEmployeeIds: string[] = []
  ): Promise<ServiceResponse<Employee[]>> {
    try {
      const supabase = await this.getSupabase();

      // Get all employees in the organization
      const { data: employees, error: employeeError } = await supabase
        .from("employees")
        .select("*")
        .eq("organization_id", organizationId)
        .eq("is_active", true)
        .not("id", "in", `(${excludeEmployeeIds.join(",")})`);

      if (employeeError) {
        return errorResponse(employeeError, `Failed to fetch employees: ${employeeError.message}`);
      }

      if (!employees) {
        return successResponse([], "No employees found");
      }

      const availableEmployees: Employee[] = [];

      // Check availability for each employee
      for (const employee of employees) {
        const availabilityCheck = await this.checkEmployeeAvailability(
          employee.id,
          "temp-appointment-id", // Temporary ID for checking
          organizationId
        );

        if (availabilityCheck.success && availabilityCheck.data?.isAvailable) {
          availableEmployees.push(employee);
        }
      }

      return successResponse(availableEmployees, "Available employees fetched successfully");
    } catch (error) {
      return errorResponse(error, "Available employees fetch failed");
    }
  }

  /**
   * Get conflict summary for dashboard
   */
  async getConflictSummary(organizationId: string): Promise<ServiceResponse<ConflictSummary>> {
    try {
      const conflictsResult = await this.detectAssignmentConflicts(organizationId);

      if (!conflictsResult.success) {
        return errorResponse(conflictsResult.error, conflictsResult.message);
      }

      const conflicts = conflictsResult.data || [];

      const summary: ConflictSummary = {
        totalConflicts: conflicts.length,
        criticalConflicts: conflicts.filter((c) => c.severity === "critical").length,
        highPriorityConflicts: conflicts.filter((c) => c.severity === "high").length,
        mediumPriorityConflicts: conflicts.filter((c) => c.severity === "medium").length,
        lowPriorityConflicts: conflicts.filter((c) => c.severity === "low").length,
        conflictsByType: conflicts.reduce(
          (acc, conflict) => {
            acc[conflict.type] = (acc[conflict.type] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ),
        resolvedToday: 0, // This would come from a resolution tracking system
      };

      return successResponse(summary, "Conflict summary generated successfully");
    } catch (error) {
      return errorResponse(error, "Conflict summary failed");
    }
  }

  /**
   * Generate resolution suggestions for conflicts
   */
  private async generateResolutions(current: any, conflicting: any): Promise<ConflictResolution[]> {
    const resolutions: ConflictResolution[] = [];

    // Suggest employee reassignment
    resolutions.push({
      type: "reassign_employee",
      description: "Assign a different employee to one of the appointments",
      feasible: true,
      alternativeEmployees: [], // Would be populated with actual available employees
    });

    // Suggest rescheduling
    resolutions.push({
      type: "reschedule_appointment",
      description: "Reschedule one of the appointments to a different time",
      feasible: true,
      alternativeTimeSlots: [], // Would be populated with available time slots
    });

    return resolutions;
  }

  /**
   * Resolve a specific conflict
   */
  async resolveConflict(
    conflictId: string,
    resolutionType: string,
    resolutionData: any,
    organizationId: string
  ): Promise<ServiceResponse<void>> {
    try {
      // This would implement the actual conflict resolution logic
      // For now, it's a placeholder

      return successResponse(undefined, "Conflict resolved successfully");
    } catch (error) {
      return errorResponse(error, "Conflict resolution failed");
    }
  }
}
