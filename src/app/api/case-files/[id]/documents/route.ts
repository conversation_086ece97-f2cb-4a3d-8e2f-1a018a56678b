import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * GET /api/case-files/[id]/documents
 * Fetch all documents attached to a case file
 */
export async function GET(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: caseFileId } = await params;

    // Get current organization
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    const supabase = await createClient();

    // Fetch case file documents (both case-level and contact-specific)
    const { data: caseDocuments, error: caseError } = await supabase
      .from("document_attachments")
      .select(
        `
        id,
        document_name,
        document_type,
        file_path,
        file_size,
        attached_to_type,
        attached_to_id,
        contact_id,
        template_id,
        attachment_type,
        status,
        metadata,
        uploaded_at,
        uploaded_by
      `
      )
      .eq("organization_id", organization.id)
      .eq("attached_to_type", "case_file")
      .eq("attached_to_id", caseFileId)
      .eq("status", "attached")
      .order("uploaded_at", { ascending: false });

    if (caseError) {
      console.error("Error fetching case documents:", caseError);
      return NextResponse.json({ error: "Failed to fetch case documents" }, { status: 500 });
    }

    // Fetch contact-specific documents via case_file_contacts junction
    const { data: junctionData, error: junctionError } = await supabase
      .from("case_file_contacts")
      .select(
        `
        id,
        contact_id,
        contacts!case_file_contacts_contact_id_fkey(
          id,
          name
        )
      `
      )
      .eq("case_file_id", caseFileId);

    if (junctionError) {
      console.error("Error fetching junction data:", junctionError);
      return NextResponse.json({ error: "Failed to fetch contact data" }, { status: 500 });
    }

    // Fetch documents for each contact
    const contactDocuments = [];
    for (const junction of junctionData || []) {
      const { data: docs, error: docError } = await supabase
        .from("document_attachments")
        .select(
          `
          id,
          document_name,
          document_type,
          file_path,
          file_size,
          attached_to_type,
          attached_to_id,
          contact_id,
          template_id,
          attachment_type,
          status,
          metadata,
          uploaded_at,
          uploaded_by
        `
        )
        .eq("organization_id", organization.id)
        .eq("attached_to_type", "case_file_contacts")
        .eq("attached_to_id", junction.id)
        .eq("status", "attached")
        .order("uploaded_at", { ascending: false });

      if (!docError && docs) {
        contactDocuments.push(
          ...docs.map((doc) => ({
            ...doc,
            contactId: junction.contact_id,
            contactName: junction.contacts?.name || "Unknown Contact",
          }))
        );
      }
    }

    // Combine and format all documents
    const allDocuments = [
      ...(caseDocuments || []).map((doc) => ({
        id: doc.id,
        name: doc.document_name,
        type: doc.document_type,
        size: doc.file_size || 0,
        uploadedAt: doc.uploaded_at,
        uploadedBy: doc.uploaded_by,
        url: `/api/documents/${doc.id}/download`,
        contactId: null,
        contactName: null,
        attachmentType: doc.attachment_type,
        metadata: doc.metadata,
      })),
      ...contactDocuments.map((doc) => ({
        id: doc.id,
        name: doc.document_name,
        type: doc.document_type,
        size: doc.file_size || 0,
        uploadedAt: doc.uploaded_at,
        uploadedBy: doc.uploaded_by,
        url: `/api/documents/${doc.id}/download`,
        contactId: doc.contactId,
        contactName: doc.contactName,
        attachmentType: doc.attachment_type,
        metadata: doc.metadata,
      })),
    ];

    // Sort by upload date (newest first)
    allDocuments.sort((a, b) => {
      const dateA = a.uploadedAt ? new Date(a.uploadedAt).getTime() : 0;
      const dateB = b.uploadedAt ? new Date(b.uploadedAt).getTime() : 0;
      return dateB - dateA;
    });

    return NextResponse.json({
      success: true,
      documents: allDocuments,
      total: allDocuments.length,
      caseDocuments: allDocuments.filter((doc) => !doc.contactId).length,
      contactDocuments: allDocuments.filter((doc) => !!doc.contactId).length,
    });
  } catch (error) {
    console.error("Error in case file documents API:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
