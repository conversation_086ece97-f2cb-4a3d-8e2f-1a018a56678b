"use server";

import { listCaseFilesAction } from "../case-file/queries";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get recent case files (last 30 days)
 * @param limit Maximum number of results to return
 * @returns ActionState with recent case files
 */
export const getRecentCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  limit: number = 20
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Calculate date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Use domain function to list recent case files
    const result = await listCaseFilesAction({
      fromDate: thirtyDaysAgo.toISOString(),
      limit,
      sortBy: "created_at",
      sortOrder: "desc",
    });

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to get recent case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting recent case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting recent case files: ${error}`,
      data: null,
    };
  }
});
