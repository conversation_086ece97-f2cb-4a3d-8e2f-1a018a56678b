"use server";

import { listCaseFilesAction } from "../case-file/queries";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get case files assigned to a specific employee
 * @param employeeId The ID of the employee
 * @param limit Maximum number of results to return
 * @returns ActionState with case files assigned to the employee
 */
export const getCaseFilesByAssignee = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  employeeId: string,
  limit: number = 50
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!employeeId) {
      return {
        success: false,
        error: "Employee ID is required",
        data: null,
      };
    }

    // Use domain function to list case files by assignee
    const result = await listCaseFilesAction({ assignedTo: employeeId, limit });

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to get case files by assignee",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case files by assignee: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case files by assignee: ${error}`,
      data: null,
    };
  }
});
