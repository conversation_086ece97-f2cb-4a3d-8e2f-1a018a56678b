"use server";

import { listCaseFilesAction } from "../case-file/queries";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Search case files by case number or contact name
 * @param query Search query string
 * @param limit Maximum number of results to return
 * @returns ActionState with matching case files
 */
export const searchCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  query: string,
  limit: number = 10
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!query || query.trim().length === 0) {
      return {
        success: true,
        error: "",
        data: [],
      };
    }

    // Use domain function to search case files
    const result = await listCaseFilesAction({
      search: query.trim(),
      limit,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to search case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error searching case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error searching case files: ${error}`,
      data: null,
    };
  }
});
