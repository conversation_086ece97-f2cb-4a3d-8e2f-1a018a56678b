"use server";

import { listCaseFilesAction } from "../case-file/queries";
import { CaseFile, CaseFileListParams } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * List case files with filtering, pagination, and sorting
 * @param params Parameters for filtering, pagination, and sorting
 * @returns ActionState with the list of case files and total count
 */
export const listCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: CaseFileListParams = {}
): Promise<ActionState<{ items: CaseFile[]; total: number } | null>> => {
  try {
    // Use domain function to list case files
    const result = await listCaseFilesAction(params);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to list case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data,
    };
  } catch (error) {
    logger.error(`Unexpected error listing case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error listing case files: ${error}`,
      data: null,
    };
  }
});
