"use server";

import { listCaseFilesAction } from "../case-file/queries";
import { CaseFileListParams, CaseFileSummary } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * List case file summaries with aggregated data for list views
 * @param params Parameters for filtering, pagination, and sorting
 * @returns ActionState with case file summaries and total count
 */
export const listCaseFileSummaries = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: CaseFileListParams = {}
): Promise<ActionState<{ items: CaseFileSummary[]; total: number }>> => {
  try {
    // Validate parameters
    if (params.page && params.page < 1) {
      return {
        success: true,
        error: "Page number must be greater than 0",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    if (params.limit && (params.limit < 1 || params.limit > 100)) {
      return {
        success: true,
        error: "Limit must be between 1 and 100",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    // Use domain function to list case files
    const result = await listCaseFilesAction(params);

    if (!result.success || !result.data) {
      return {
        success: true,
        error: result.error || "",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    // Transform to summaries
    const summaries: CaseFileSummary[] = result.data.items.map((caseFile) => ({
      id: caseFile.id,
      case_number: caseFile.case_number,
      status: caseFile.status,
      created_at: caseFile.created_at,
      activated_at: caseFile.activated_at,
      suspended_at: caseFile.suspended_at,
      closed_at: caseFile.closed_at,
      assigned_to: caseFile.assigned_to,
      primaryContactName: undefined, // Will be populated by individual calls if needed
      totalContacts: 0, // Will be populated by individual calls if needed
      upcomingAppointments: 0, // Will be populated when scheduling features are added
      pendingDocuments: 0, // Will be populated when document features are added
    }));

    return {
      success: true,
      error: "",
      data: {
        items: summaries,
        total: result.data.total,
      },
    };
  } catch (error) {
    logger.error(`Unexpected error listing case file summaries: ${error}`);
    return {
      success: true,
      error: `Unexpected error listing case file summaries: ${error}`,
      data: {
        items: [],
        total: 0,
      },
    };
  }
});
