"use server";

import { getCaseFileDashboardData as domainGetCaseFileDashboardData } from "../lib/case-file/queries/getCaseFileDashboardData";
import { getCaseFileStatistics as domainGetCaseFileStatistics } from "../lib/case-file/queries/getCaseFileStatistics";
import { toDashboardData } from "../lib/case-file/transformers/toDashboardData";
import {
  CaseFileWithRelations,
  CaseFileDashboardData,
  CaseFileSummary,
  CaseFileStatus,
} from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * Get comprehensive dashboard data for an active case file
 * @param caseFileId The ID of the case file
 * @returns ActionState with aggregated dashboard data
 */
export const getCaseFileDashboardData = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileDashboardData | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get dashboard data using domain function
    const domainDashboardData = await domainGetCaseFileDashboardData(caseFileId);

    // Transform to legacy format
    const dashboardData = toDashboardData(domainDashboardData);

    return {
      success: true,
      error: "",
      data: dashboardData,
    };
  } catch (error) {
    logger.error(`Unexpected error getting dashboard data: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting dashboard data: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file summary data for cards and lists
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file summary
 */
export const getCaseFileSummaryData = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileSummary | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file data using new domain function
    const { getCaseFileWithRelations: domainGetCaseFileWithRelations } = await import(
      "./case-file/queries"
    );

    const result = await domainGetCaseFileWithRelations(caseFileId);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to get case file summary",
        data: null,
      };
    }

    // Convert to summary format
    const summary: CaseFileSummary = {
      id: result.data.id,
      case_number: result.data.caseNumber,
      status: result.data.status,
      created_at: result.data.createdAt,
      activated_at: result.data.activatedAt,
      suspended_at: result.data.suspendedAt,
      closed_at: result.data.closedAt,
      assigned_to: result.data.assignedTo,
      primaryContactName: result.data.contacts?.[0]?.name,
      totalContacts: result.data.contacts?.length || 0,
      upcomingAppointments: 0, // Will be populated when scheduling features are added
      pendingDocuments: 0, // Will be populated when document features are added
    };

    return {
      success: true,
      error: "",
      data: summary,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file summary: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file summary: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file contacts with relationship information
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file contacts
 */
export const getCaseFileContacts = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileWithRelations["contacts"] | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file with contacts using new domain function
    const { getCaseFileWithRelations: domainGetCaseFileWithRelations } = await import(
      "./case-file/queries"
    );

    const result = await domainGetCaseFileWithRelations(caseFileId);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to get case file contacts",
        data: null,
      };
    }

    // Convert contacts to expected format
    const contacts =
      result.data.contacts?.map((contact) => ({
        id: contact.id,
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        relationship_type: contact.relationshipType || "",
      })) || [];

    return {
      success: true,
      error: "",
      data: contacts,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file contacts: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file contacts: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file with full details and relations
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file and relations
 */
export const getCaseFileWithRelations = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileWithRelations | null>> => {
  try {
    // Import the new domain function
    const { getCaseFileWithRelations: newGetCaseFileWithRelations } = await import(
      "./case-file/queries"
    );

    // Use the new domain-driven implementation
    const result = await newGetCaseFileWithRelations(caseFileId);

    // Map domain model to interface format
    if (result.success && result.data) {
      const caseFileData: CaseFileWithRelations = {
        id: result.data.id,
        case_number: result.data.caseNumber,
        status: result.data.status,
        organization_id: result.data.organizationId,
        request_id: result.data.requestId || "",
        created_at: result.data.createdAt,
        updated_at: result.data.updatedAt,
        opened_at: result.data.openedAt || null,
        activated_at: result.data.activatedAt || null,
        suspended_at: result.data.suspendedAt || null,
        closed_at: result.data.closedAt || null,
        assigned_to: result.data.assignedTo || null,
        created_by: result.data.createdBy || "",
        metadata: result.data.metadata,
        employees: result.data.employee
          ? {
              id: result.data.employee.id,
              first_name: result.data.employee.firstName,
              last_name: result.data.employee.lastName,
              email: result.data.employee.email,
            }
          : undefined,
        contacts: result.data.contacts?.map((contact) => ({
          id: contact.id,
          name: contact.name,
          email: contact.email,
          phone: contact.phone,
          relationship_type: contact.relationshipType || "",
        })),
      };

      return {
        success: true,
        error: "",
        data: caseFileData,
      };
    }

    return {
      success: false,
      error: result.error || "Failed to get case file",
      data: null,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file statistics for organization dashboard
 * @returns ActionState with case file statistics
 */
export const getCaseFileStatistics = requirePermission(DOMAIN_PERMISSIONS.VIEW)(
  async (): Promise<
    ActionState<{
      opening: number;
      active: number;
      suspended: number;
      closed: number;
      total: number;
    } | null>
  > => {
    try {
      // Get statistics using domain function
      const statistics = await domainGetCaseFileStatistics();

      return {
        success: true,
        error: "",
        data: statistics,
      };
    } catch (error) {
      logger.error(`Unexpected error getting case file statistics: ${error}`);
      return {
        success: false,
        error: `Unexpected error getting case file statistics: ${error}`,
        data: null,
      };
    }
  }
);

/**
 * Get active case files for dashboard
 * @returns ActionState with active case files
 */
export const getActiveCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(
  async (): Promise<ActionState<CaseFileSummary[] | null>> => {
    try {
      // Get active case files using new domain function
      const { listCaseFilesAction } = await import("./case-file/queries");

      const result = await listCaseFilesAction({ status: "active" });

      if (!result.success || !result.data) {
        return {
          success: false,
          error: result.error || "Failed to get active case files",
          data: null,
        };
      }

      // Convert to summaries for dashboard display
      const summaries: CaseFileSummary[] = result.data.items.map((caseFile) => ({
        id: caseFile.id,
        case_number: caseFile.case_number,
        status: caseFile.status,
        created_at: caseFile.created_at,
        activated_at: caseFile.activated_at,
        suspended_at: caseFile.suspended_at,
        closed_at: caseFile.closed_at,
        assigned_to: caseFile.assigned_to,
        primaryContactName: undefined, // Will be populated by individual summary calls if needed
        totalContacts: 0, // Will be populated by individual summary calls if needed
        upcomingAppointments: 0, // Will be populated when scheduling features are added
        pendingDocuments: 0, // Will be populated when document features are added
      }));

      return {
        success: true,
        error: "",
        data: summaries,
      };
    } catch (error) {
      logger.error(`Error getting active case files: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get active case files",
        data: null,
      };
    }
  }
);

/**
 * Get case file status for routing decisions
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file status
 */
export const getCaseFileStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<{ status: CaseFileStatus } | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file status using new domain function
    const { getCaseFileStatus: domainGetCaseFileStatus } = await import("./case-file/queries");

    const result = await domainGetCaseFileStatus(caseFileId);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Case file not found",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file status: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file status: ${error}`,
      data: null,
    };
  }
});
