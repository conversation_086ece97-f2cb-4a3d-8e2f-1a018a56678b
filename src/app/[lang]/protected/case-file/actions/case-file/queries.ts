"use server";

import { getCaseFileWithDetails } from "../../lib/case-file/queries/getCaseFileWithDetails";
import { listCaseFiles } from "../../lib/case-file/queries/getCaseFile";
import { createSupabaseClient } from "../../lib/shared/database/supabaseClient";
import type { ActionState } from "../../lib/shared/types/common";
import type { CaseFileWithDetails } from "../../lib/case-file/types";
import type { ListParams, CaseFileStatus } from "../../lib/shared/types/database";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get case file with full details and relations
 * @param id Case file ID
 * @returns ActionState with case file details
 */
export const getCaseFileWithRelations = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<CaseFileWithDetails | null>> => {
  try {
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    const data = await getCaseFileWithDetails(id);

    return {
      success: true,
      error: "",
      data,
    };
  } catch (error) {
    logger.error(`Error getting case file with relations: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get case file",
      data: null,
    };
  }
});

/**
 * List case files with filters and pagination
 * @param params List parameters
 * @returns ActionState with case files list
 */
export const listCaseFilesAction = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: ListParams = {}
): Promise<ActionState<{ items: any[]; total: number } | null>> => {
  try {
    const supabase = await createSupabaseClient();
    const result = await listCaseFiles(supabase, params);

    return {
      success: true,
      error: "",
      data: result,
    };
  } catch (error) {
    logger.error(`Error listing case files: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to list case files",
      data: null,
    };
  }
});

/**
 * Get case file status for routing decisions
 * @param id Case file ID
 * @returns ActionState with case file status
 */
export const getCaseFileStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<{ status: CaseFileStatus } | null>> => {
  try {
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    const supabase = await createSupabaseClient();
    const { data, error } = await supabase
      .from("case_files")
      .select("status")
      .eq("id", id)
      .single();

    if (error) {
      return {
        success: false,
        error: "Case file not found",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: { status: data.status as CaseFileStatus },
    };
  } catch (error) {
    logger.error(`Error getting case file status: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get case file status",
      data: null,
    };
  }
});
