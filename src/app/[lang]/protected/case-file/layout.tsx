import { ReactNode } from "react";

interface CaseFileLayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Layout for Case File domain
 * Provides consistent layout structure for all case file pages
 */
export default async function CaseFileLayout({ children, params }: CaseFileLayoutProps) {
  const {} = await params;

  return <div className="container mx-auto px-4 py-6">{children}</div>;
}
