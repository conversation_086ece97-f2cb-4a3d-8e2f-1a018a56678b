import type { SupabaseClient } from "@supabase/supabase-js";
import type { ContactRow } from "../../shared/types/database";
import type { ContactWithRelationship } from "../types";
import { NotFoundError } from "../../shared/types/common";

/**
 * Get contact by ID
 * @param supabase Supabase client
 * @param id Contact ID
 * @returns Contact data or throws NotFoundError
 */
export const getContact = async (supabase: SupabaseClient, id: string): Promise<ContactRow> => {
  const { data, error } = await supabase.from("contacts").select("*").eq("id", id).single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Contact", id);
    }
    throw new Error(`Failed to get contact: ${error.message}`);
  }

  return data as ContactRow;
};

/**
 * Get contacts by case file ID with relationship information
 * @param supabase Supabase client
 * @param caseFileId Case file ID
 * @returns Array of contacts with relationship data
 */
export const getContactsByCaseFile = async (
  supabase: SupabaseClient,
  caseFileId: string
): Promise<ContactWithRelationship[]> => {
  const { data, error } = await supabase
    .from("case_file_contacts")
    .select(
      `
      relationship_type,
      contacts!case_file_contacts_contact_id_fkey(
        id,
        name,
        email,
        phone,
        address,
        status,
        created_at,
        updated_at
      )
    `
    )
    .eq("case_file_id", caseFileId);

  if (error) {
    throw new Error(`Failed to get contacts for case file: ${error.message}`);
  }

  // Transform the joined data to include relationship_type
  return (data || []).map((item: any) => ({
    ...item.contacts,
    relationship_type: item.relationship_type,
  })) as ContactWithRelationship[];
};

/**
 * Get multiple contacts by IDs
 * @param supabase Supabase client
 * @param ids Array of contact IDs
 * @returns Array of contact data
 */
export const getContacts = async (
  supabase: SupabaseClient,
  ids: string[]
): Promise<ContactRow[]> => {
  if (ids.length === 0) return [];

  const { data, error } = await supabase.from("contacts").select("*").in("id", ids);

  if (error) {
    throw new Error(`Failed to get contacts: ${error.message}`);
  }

  return (data || []) as ContactRow[];
};
