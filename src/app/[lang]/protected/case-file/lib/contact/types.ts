import type { ContactRow } from "../shared/types/database";

/**
 * Contact domain types
 */

/**
 * Contact with extracted email and phone for UI display
 */
export interface ContactInfo {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  relationshipType?: string;
  status?: string;
}

/**
 * Contact with full information
 */
export interface ContactDetails extends ContactInfo {
  address?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Case file contact with relationship information
 */
export interface CaseFileContactInfo extends ContactInfo {
  relationshipType: string;
  caseFileId: string;
}

/**
 * Contact query parameters
 */
export interface ContactQueryParams {
  includeEmail?: boolean;
  includePhone?: boolean;
  includeAddress?: boolean;
  includeRelationship?: boolean;
}

/**
 * Contact filters
 */
export interface ContactFilters {
  status?: string | string[];
  relationshipType?: string;
  caseFileId?: string;
  organizationId?: string;
  search?: string;
}

/**
 * Contact with relationship data (from case_file_contacts join)
 */
export interface ContactWithRelationship extends ContactRow {
  relationship_type: string;
}
