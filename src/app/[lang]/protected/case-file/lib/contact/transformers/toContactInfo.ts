import type { ContactRow } from "../../shared/types/database";
import type {
  ContactInfo,
  ContactDetails,
  CaseFileContactInfo,
  ContactWithRelationship,
} from "../types";
import {
  extractContactEmail,
  extractContactPhone,
} from "../../shared/transformers/jsonbExtractors";

/**
 * Transform contact row to contact info
 * @param contact Raw contact data from database
 * @returns Contact with extracted email and phone
 */
export const toContactInfo = (contact: ContactRow): ContactInfo => ({
  id: contact.id,
  name: contact.name,
  email: extractContactEmail(contact.email),
  phone: extractContactPhone(contact.phone),
  status: contact.status,
});

/**
 * Transform contact row to contact details
 * @param contact Raw contact data from database
 * @returns Contact with full details
 */
export const toContactDetails = (contact: ContactRow): ContactDetails => ({
  id: contact.id,
  name: contact.name,
  email: extractContactEmail(contact.email),
  phone: extractContactPhone(contact.phone),
  address: contact.address || undefined,
  status: contact.status,
  createdAt: contact.created_at || "",
  updatedAt: contact.updated_at || "",
});

/**
 * Transform contact with relationship to case file contact info
 * @param contact Contact data with relationship information
 * @returns Case file contact info
 */
export const toCaseFileContactInfo = (contact: ContactWithRelationship): CaseFileContactInfo => ({
  id: contact.id,
  name: contact.name,
  email: extractContactEmail(contact.email),
  phone: extractContactPhone(contact.phone),
  relationshipType: contact.relationship_type,
  status: contact.status,
  caseFileId: "", // This would be set by the calling context
});

/**
 * Transform array of contacts to contact info array
 * @param contacts Array of raw contact data
 * @returns Array of contacts with extracted emails and phones
 */
export const toContactInfoArray = (contacts: ContactRow[]): ContactInfo[] =>
  contacts.map(toContactInfo);

/**
 * Transform array of contacts with relationships to case file contact info array
 * @param contacts Array of contacts with relationship data
 * @param caseFileId Case file ID to include in the result
 * @returns Array of case file contact info
 */
export const toCaseFileContactInfoArray = (
  contacts: ContactWithRelationship[],
  caseFileId: string
): CaseFileContactInfo[] =>
  contacts.map((contact) => ({
    ...toCaseFileContactInfo(contact),
    caseFileId,
  }));

/**
 * Transform array of contact rows to contact details array
 * @param contacts Array of raw contact data
 * @returns Array of contact details
 */
export const toContactDetailsArray = (contacts: ContactRow[]): ContactDetails[] =>
  contacts.map(toContactDetails);
