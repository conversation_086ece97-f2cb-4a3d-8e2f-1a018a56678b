import { createClient } from "@/lib/supabase/server";
import type { SupabaseClient } from "@supabase/supabase-js";

/**
 * Create a fresh Supabase client for server-side operations
 * @returns Promise<SupabaseClient>
 */
export const createSupabaseClient = async (): Promise<SupabaseClient> => {
  return await createClient();
};

/**
 * Generic database query helper with error handling
 * @param queryFn Function that performs the database query
 * @returns Promise with the query result
 */
export const executeQuery = async <T>(
  queryFn: (supabase: SupabaseClient) => Promise<{ data: T | null; error: any }>
): Promise<T> => {
  const supabase = await createSupabaseClient();
  const { data, error } = await queryFn(supabase);

  if (error) {
    throw new Error(error.message);
  }

  if (!data) {
    throw new Error("No data returned from query");
  }

  return data;
};

/**
 * Generic database query helper for optional results
 * @param queryFn Function that performs the database query
 * @returns Promise with the query result or null
 */
export const executeOptionalQuery = async <T>(
  queryFn: (supabase: SupabaseClient) => Promise<{ data: T | null; error: any }>
): Promise<T | null> => {
  const supabase = await createSupabaseClient();
  const { data, error } = await queryFn(supabase);

  if (error) {
    // For optional queries, we return null instead of throwing
    return null;
  }

  return data;
};
