import { Database } from "@/lib/types/database.types";

/**
 * Raw database row types (single source of truth)
 */
export type CaseFileRow = Database["public"]["Tables"]["case_files"]["Row"];
export type CaseFileInsert = Database["public"]["Tables"]["case_files"]["Insert"];
export type CaseFileUpdate = Database["public"]["Tables"]["case_files"]["Update"];

export type EmployeeRow = Database["public"]["Tables"]["employees"]["Row"];
export type EmployeeInsert = Database["public"]["Tables"]["employees"]["Insert"];
export type EmployeeUpdate = Database["public"]["Tables"]["employees"]["Update"];

export type ContactRow = Database["public"]["Tables"]["contacts"]["Row"];
export type ContactInsert = Database["public"]["Tables"]["contacts"]["Insert"];
export type ContactUpdate = Database["public"]["Tables"]["contacts"]["Update"];

export type CaseFileContactRow = Database["public"]["Tables"]["case_file_contacts"]["Row"];
export type CaseFileContactInsert = Database["public"]["Tables"]["case_file_contacts"]["Insert"];
export type CaseFileContactUpdate = Database["public"]["Tables"]["case_file_contacts"]["Update"];

export type CaseFileHistoryRow = Database["public"]["Tables"]["case_file_history"]["Row"];
export type CaseFileHistoryInsert = Database["public"]["Tables"]["case_file_history"]["Insert"];

/**
 * Enums
 */
export type CaseFileStatus = Database["public"]["Enums"]["case_file_status"];

/**
 * Common query filters
 */
export interface CaseFileFilters {
  status?: CaseFileStatus | CaseFileStatus[];
  assignedTo?: string;
  createdBy?: string;
  search?: string;
  fromDate?: string;
  toDate?: string;
  organizationId?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface ListParams extends CaseFileFilters, PaginationParams {}

/**
 * Generic service response type
 */
export interface ServiceResponse<T> {
  success: boolean;
  data: T | null;
  message: string;
}
