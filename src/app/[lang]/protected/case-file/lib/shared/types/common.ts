/**
 * Common domain types used across multiple domains
 */

/**
 * Standard action state for server actions
 */
export interface ActionState<T> {
  success: boolean;
  data: T | null;
  error: string;
}

/**
 * Generic list response with pagination
 */
export interface ListResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Generic query parameters
 */
export interface QueryParams {
  [key: string]: string | number | boolean | undefined;
}

/**
 * Generic transformation context
 */
export interface TransformationContext {
  includeRelations?: boolean;
  includeHistory?: boolean;
  includeContacts?: boolean;
  includeEmployee?: boolean;
}

/**
 * Error types
 */
export class DomainError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = "DomainError";
  }
}

export class NotFoundError extends DomainError {
  constructor(resource: string, id: string) {
    super(`${resource} with ID ${id} not found`, "NOT_FOUND", { resource, id });
    this.name = "NotFoundError";
  }
}

export class ValidationError extends DomainError {
  constructor(message: string, details?: any) {
    super(message, "VALIDATION_ERROR", details);
    this.name = "ValidationError";
  }
}
