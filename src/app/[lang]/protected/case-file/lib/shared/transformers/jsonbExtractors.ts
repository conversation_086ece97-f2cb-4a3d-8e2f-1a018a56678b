/**
 * Shared JSONB extraction utilities
 * Handles extraction of data from JSONB fields in a consistent way
 */

/**
 * Extract primary email from employee emails JSONB array
 * @param emails The emails JSON<PERSON> array from employee record
 * @returns Primary email or undefined
 */
export const extractEmployeeEmail = (emails: any): string | undefined => {
  if (!emails || !Array.isArray(emails)) return undefined;

  // Find primary email first
  const primaryEmail = emails.find((email: any) => email.primary === true);
  if (primaryEmail?.email) return primaryEmail.email;

  // Fall back to work email
  const workEmail = emails.find((email: any) => email.type === "work");
  if (workEmail?.email) return workEmail.email;

  // Fall back to first email
  if (emails.length > 0 && emails[0]?.email) return emails[0].email;

  return undefined;
};

/**
 * Extract email from contact email JSONB object
 * @param emailData The email JSONB object from contact record
 * @returns Primary email or undefined
 */
export const extractContactEmail = (emailData: any): string | undefined => {
  if (!emailData) return undefined;

  // Handle different email data structures
  if (typeof emailData === "string") return emailData;
  if (typeof emailData === "object") {
    return emailData.personal || emailData.work || emailData.main || emailData.primary;
  }

  return undefined;
};

/**
 * Extract phone from contact phone JSONB object
 * @param phoneData The phone JSONB object from contact record
 * @returns Primary phone or undefined
 */
export const extractContactPhone = (phoneData: any): string | undefined => {
  if (!phoneData) return undefined;

  // Handle different phone data structures
  if (typeof phoneData === "string") return phoneData;
  if (typeof phoneData === "object") {
    return (
      phoneData.mobile || phoneData.home || phoneData.work || phoneData.main || phoneData.primary
    );
  }

  return undefined;
};

/**
 * Extract primary phone from employee phones JSONB array
 * @param phones The phones JSONB array from employee record
 * @returns Primary phone or undefined
 */
export const extractEmployeePhone = (phones: any): string | undefined => {
  if (!phones || !Array.isArray(phones)) return undefined;

  // Find primary phone first
  const primaryPhone = phones.find((phone: any) => phone.primary === true);
  if (primaryPhone?.number) return primaryPhone.number;

  // Fall back to work phone
  const workPhone = phones.find((phone: any) => phone.type === "work");
  if (workPhone?.number) return workPhone.number;

  // Fall back to mobile phone
  const mobilePhone = phones.find((phone: any) => phone.type === "mobile");
  if (mobilePhone?.number) return mobilePhone.number;

  // Fall back to first phone
  if (phones.length > 0 && phones[0]?.number) return phones[0].number;

  return undefined;
};
