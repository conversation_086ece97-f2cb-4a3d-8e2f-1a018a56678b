import type { EmployeeRow } from "../../shared/types/database";
import type { EmployeeWithEmail, EmployeeSummary, EmployeeWithContacts } from "../types";
import {
  extractEmployeeEmail,
  extractEmployeePhone,
} from "../../shared/transformers/jsonbExtractors";

/**
 * Transform employee row to employee with email
 * @param employee Raw employee data from database
 * @returns Employee with extracted email
 */
export const toEmployeeWithEmail = (employee: EmployeeRow): EmployeeWithEmail => ({
  id: employee.id,
  firstName: employee.first_name,
  lastName: employee.last_name,
  email: extractEmployeeEmail(employee.emails),
  jobTitle: employee.job_title || undefined,
  department: employee.department || undefined,
});

/**
 * Transform employee row to employee summary
 * @param employee Raw employee data from database
 * @returns Employee summary for lists
 */
export const toEmployeeSummary = (employee: EmployeeRow): EmployeeSummary => ({
  id: employee.id,
  firstName: employee.first_name,
  lastName: employee.last_name,
  email: extractEmployeeEmail(employee.emails),
  jobTitle: employee.job_title || undefined,
  employmentStatus: employee.employment_status,
});

/**
 * Transform employee row to employee with full contacts
 * @param employee Raw employee data from database
 * @param supervisor Optional supervisor data
 * @returns Employee with full contact information
 */
export const toEmployeeWithContacts = (
  employee: EmployeeRow,
  supervisor?: EmployeeRow
): EmployeeWithContacts => ({
  id: employee.id,
  firstName: employee.first_name,
  lastName: employee.last_name,
  email: extractEmployeeEmail(employee.emails),
  phone: extractEmployeePhone(employee.phones),
  jobTitle: employee.job_title || undefined,
  department: employee.department || undefined,
  address: employee.address,
  employmentStatus: employee.employment_status,
  hireDate: employee.hire_date || undefined,
  supervisor: supervisor ? toEmployeeSummary(supervisor) : undefined,
});

/**
 * Transform array of employee rows to employees with email
 * @param employees Array of raw employee data
 * @returns Array of employees with extracted emails
 */
export const toEmployeesWithEmail = (employees: EmployeeRow[]): EmployeeWithEmail[] =>
  employees.map(toEmployeeWithEmail);

/**
 * Transform array of employee rows to employee summaries
 * @param employees Array of raw employee data
 * @returns Array of employee summaries
 */
export const toEmployeeSummaries = (employees: EmployeeRow[]): EmployeeSummary[] =>
  employees.map(toEmployeeSummary);
