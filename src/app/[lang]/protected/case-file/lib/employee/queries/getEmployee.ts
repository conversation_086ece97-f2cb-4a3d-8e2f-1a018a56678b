import type { SupabaseClient } from "@supabase/supabase-js";
import type { EmployeeRow } from "../../shared/types/database";
import { NotFoundError } from "../../shared/types/common";

/**
 * Get employee by ID
 * @param supabase Supabase client
 * @param id Employee ID
 * @returns Employee data or throws NotFoundError
 */
export const getEmployee = async (supabase: SupabaseClient, id: string): Promise<EmployeeRow> => {
  const { data, error } = await supabase.from("employees").select("*").eq("id", id).single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Employee", id);
    }
    throw new Error(`Failed to get employee: ${error.message}`);
  }

  return data as EmployeeRow;
};

/**
 * Get employee by ID (optional - returns null if not found)
 * @param supabase Supabase client
 * @param id Employee ID
 * @returns Employee data or null
 */
export const getEmployeeOptional = async (
  supabase: SupabaseClient,
  id: string
): Promise<EmployeeRow | null> => {
  try {
    return await getEmployee(supabase, id);
  } catch (error) {
    if (error instanceof NotFoundError) {
      return null;
    }
    throw error;
  }
};

/**
 * Get multiple employees by IDs
 * @param supabase Supabase client
 * @param ids Array of employee IDs
 * @returns Array of employee data
 */
export const getEmployees = async (
  supabase: SupabaseClient,
  ids: string[]
): Promise<EmployeeRow[]> => {
  if (ids.length === 0) return [];

  const { data, error } = await supabase.from("employees").select("*").in("id", ids);

  if (error) {
    throw new Error(`Failed to get employees: ${error.message}`);
  }

  return (data || []) as EmployeeRow[];
};
