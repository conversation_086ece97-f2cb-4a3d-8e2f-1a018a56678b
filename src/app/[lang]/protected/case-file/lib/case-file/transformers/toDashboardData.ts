import type { CaseFileWithRelations, CaseFileDashboardData } from "../../types";
import type { DashboardData } from "../queries/getCaseFileDashboardData";
import type { CaseFileWithDetails } from "../types";

/**
 * Transform domain dashboard data to legacy dashboard data format
 * @param domainData Dashboard data from domain layer
 * @returns Legacy dashboard data format
 */
export function toDashboardData(domainData: DashboardData): CaseFileDashboardData {
  // Transform case file from domain format to legacy format
  const caseFile: CaseFileWithRelations = transformCaseFile(domainData.caseFile);

  return {
    caseFile,
    familyInfo: domainData.familyInfo,
    serviceRequirements: domainData.serviceRequirements,
    recentActivity: domainData.recentActivity.map((activity) => ({
      ...activity,
      type: activity.type as "appointment" | "document" | "note" | "status_change",
    })),
  };
}

/**
 * Transform CaseFileWithDetails to CaseFileWithRelations
 */
function transformCaseFile(domainCaseFile: CaseFileWithDetails): CaseFileWithRelations {
  return {
    id: domainCaseFile.id,
    case_number: domainCaseFile.caseNumber,
    status: domainCaseFile.status,
    created_at: domainCaseFile.createdAt,
    activated_at: domainCaseFile.activatedAt || null,
    suspended_at: domainCaseFile.suspendedAt || null,
    closed_at: domainCaseFile.closedAt || null,
    assigned_to: domainCaseFile.assignedTo || null,
    request_id: domainCaseFile.requestId || "",
    updated_at: domainCaseFile.updatedAt || domainCaseFile.createdAt,
    created_by: "", // TODO: Add to domain type when needed
    metadata: null, // TODO: Add to domain type when needed
    opened_at: null, // TODO: Add to domain type when needed
    organization_id: "", // TODO: Add to domain type when needed
    contacts: domainCaseFile.contacts?.map((contact) => ({
      id: contact.id,
      name: contact.name,
      email: contact.email,
      phone: contact.phone,
      relationship_type: contact.relationshipType,
    })),
    request: (domainCaseFile as any).request
      ? {
          id: (domainCaseFile as any).request.id,
          reference_number: (domainCaseFile as any).request.referenceNumber,
          title: (domainCaseFile as any).request.title,
          description: (domainCaseFile as any).request.description,
          service: (domainCaseFile as any).request.service
            ? {
                id: (domainCaseFile as any).request.service.id,
                name: (domainCaseFile as any).request.service.name,
                description: (domainCaseFile as any).request.service.description,
              }
            : undefined,
        }
      : undefined,
    employees: domainCaseFile.employee
      ? {
          id: domainCaseFile.employee.id,
          first_name: domainCaseFile.employee.firstName,
          last_name: domainCaseFile.employee.lastName,
          email: domainCaseFile.employee.email,
        }
      : undefined,
  };
}
