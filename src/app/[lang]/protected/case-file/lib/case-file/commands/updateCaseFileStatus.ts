"use server";

import { createSupabaseClient } from "../../shared/database/supabaseClient";
import { logger } from "@/lib/logger/services/LoggerService";
import type { CaseFileStatus } from "../../shared/types/database";

/**
 * Update case file status with audit trail
 * @param id Case file ID
 * @param status New status
 * @param notes Optional notes for the status change
 * @returns Updated case file data
 */
export async function updateCaseFileStatus(
  id: string,
  status: CaseFileStatus,
  notes?: string
): Promise<{ id: string; status: CaseFileStatus }> {
  const supabase = await createSupabaseClient();

  // Get current case file data for audit trail
  const { data: currentData, error: fetchError } = await supabase
    .from("case_files")
    .select("status, case_number")
    .eq("id", id)
    .single();

  if (fetchError) {
    logger.error(`Error fetching case file for status update: ${fetchError.message}`);
    throw new Error("Case file not found");
  }

  // Update the case file status with appropriate timestamp
  const updateData: any = { status };

  switch (status) {
    case "active":
      updateData.activated_at = new Date().toISOString();
      break;
    case "suspended":
      updateData.suspended_at = new Date().toISOString();
      break;
    case "closed":
      updateData.closed_at = new Date().toISOString();
      break;
  }

  const { data, error } = await supabase
    .from("case_files")
    .update(updateData)
    .eq("id", id)
    .select("id, status")
    .single();

  if (error) {
    logger.error(`Error updating case file status: ${error.message}`);
    throw new Error("Failed to update case file status");
  }

  // Create audit trail entry
  try {
    await supabase.from("case_file_history").insert({
      case_file_id: id,
      action: `status_changed_to_${status}`,
      changes: {
        from_status: currentData.status,
        to_status: status,
        notes: notes || null,
      },
      created_at: new Date().toISOString(),
    });
  } catch (historyError) {
    logger.warn(`Failed to create history entry: ${historyError}`);
    // Don't fail the main operation if history fails
  }

  logger.info(
    `Case file ${currentData.case_number} status updated from ${currentData.status} to ${status}`
  );

  return data;
}
