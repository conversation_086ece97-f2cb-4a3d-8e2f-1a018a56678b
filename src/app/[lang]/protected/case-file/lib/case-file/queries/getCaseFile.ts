import type { SupabaseClient } from "@supabase/supabase-js";
import type { CaseFileRow, ListParams } from "../../shared/types/database";
import { NotFoundError } from "../../shared/types/common";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Get case file by ID
 * @param supabase Supabase client
 * @param id Case file ID
 * @returns Case file data or throws NotFoundError
 */
export const getCaseFile = async (supabase: SupabaseClient, id: string): Promise<CaseFileRow> => {
  // Get current organization for security
  const organization = await ProfileService.getCurrentOrganization();
  if (!organization) {
    throw new Error("Organization not found");
  }

  const { data, error } = await supabase
    .from("case_files")
    .select("*")
    .eq("id", id)
    .eq("organization_id", organization.id)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new NotFoundError("Case file", id);
    }
    throw new Error(`Failed to get case file: ${error.message}`);
  }

  return data as CaseFileRow;
};

/**
 * List case files with filters and pagination
 * @param supabase Supabase client
 * @param params List parameters with filters and pagination
 * @returns Array of case files and total count
 */
export const listCaseFiles = async (
  supabase: SupabaseClient,
  params: ListParams = {}
): Promise<{ items: CaseFileRow[]; total: number }> => {
  // Get current organization for security
  const organization = await ProfileService.getCurrentOrganization();
  if (!organization) {
    throw new Error("Organization not found");
  }

  const {
    page = 1,
    limit = 10,
    status,
    search,
    sortBy = "created_at",
    sortOrder = "desc",
    assignedTo,
    createdBy,
    fromDate,
    toDate,
  } = params;

  const offset = (page - 1) * limit;

  // Build query
  let query = supabase
    .from("case_files")
    .select("*", { count: "exact" })
    .eq("organization_id", organization.id);

  // Apply filters
  if (status) {
    if (Array.isArray(status)) {
      query = query.in("status", status);
    } else {
      query = query.eq("status", status);
    }
  }

  if (search) {
    query = query.or(`case_number.ilike.%${search}%`);
  }

  if (assignedTo) {
    query = query.eq("assigned_to", assignedTo);
  }

  if (createdBy) {
    query = query.eq("created_by", createdBy);
  }

  if (fromDate) {
    query = query.gte("created_at", fromDate);
  }

  if (toDate) {
    query = query.lte("created_at", toDate);
  }

  // Apply sorting and pagination
  query = query.order(sortBy, { ascending: sortOrder === "asc" }).range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to list case files: ${error.message}`);
  }

  return {
    items: (data || []) as CaseFileRow[],
    total: count || 0,
  };
};
