"use server";

import { getCaseFileWithDetails } from "./getCaseFileWithDetails";
import { createSupabaseClient } from "../../shared/database/supabaseClient";
import { logger } from "@/lib/logger/services/LoggerService";
import type { CaseFileWithDetails } from "../types";

export interface DashboardData {
  caseFile: CaseFileWithDetails;
  familyInfo: {
    primaryContact?: {
      name: string;
      relationship: string;
      phone?: string;
      email?: string;
    };
    children: Array<{
      name: string;
      age: number;
      relationship: string;
    }>;
    totalContacts: number;
  };
  serviceRequirements: {
    totalServices: number;
    completedServices: number;
    upcomingAppointments: number;
    pendingDocuments: number;
  };
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

/**
 * Get comprehensive dashboard data for a case file
 * @param caseFileId Case file ID
 * @returns Dashboard data with aggregated information
 */
export async function getCaseFileDashboardData(caseFileId: string): Promise<DashboardData> {
  try {
    // Get case file with details
    const caseFile = await getCaseFileWithDetails(caseFileId);
    if (!caseFile) {
      throw new Error("Case file not found");
    }

    // Aggregate family information
    const familyInfo = aggregateFamilyInfo(caseFile);

    // Get service requirements (placeholder for now)
    const serviceRequirements = await getServiceRequirements(caseFileId);

    // Get recent activity
    const recentActivity = await getRecentActivity(caseFileId);

    return {
      caseFile,
      familyInfo,
      serviceRequirements,
      recentActivity,
    };
  } catch (error) {
    logger.error(`Error getting dashboard data: ${error}`);
    throw error;
  }
}

/**
 * Aggregate family information from case file contacts
 */
function aggregateFamilyInfo(caseFile: CaseFileWithDetails) {
  const contacts = caseFile.contacts || [];

  // Find primary contact (parent or guardian)
  const primaryContact = contacts.find(
    (contact) => contact.relationshipType === "parent" || contact.relationshipType === "guardian"
  );

  // Find children
  const children = contacts
    .filter((contact) => contact.relationshipType === "child")
    .map((contact) => ({
      name: contact.name,
      age: 8, // Placeholder - would calculate from birth date
      relationship: contact.relationshipType,
    }));

  return {
    primaryContact: primaryContact
      ? {
          name: primaryContact.name,
          relationship: primaryContact.relationshipType,
          phone: primaryContact.phone,
          email: primaryContact.email,
        }
      : undefined,
    children,
    totalContacts: contacts.length,
  };
}

/**
 * Get service requirements (placeholder implementation)
 */
async function getServiceRequirements(_caseFileId: string) {
  // TODO: Implement when scheduling and document features are added
  return {
    totalServices: 4,
    completedServices: 1,
    upcomingAppointments: 0,
    pendingDocuments: 0,
  };
}

/**
 * Get recent activity from case file history
 */
async function getRecentActivity(caseFileId: string) {
  const supabase = await createSupabaseClient();

  try {
    const { data, error } = await supabase
      .from("case_file_history")
      .select("*")
      .eq("case_file_id", caseFileId)
      .order("created_at", { ascending: false })
      .limit(5);

    if (error) {
      logger.error(`Error getting recent activity: ${error.message}`);
      return [];
    }

    return (data || [])
      .filter((item) => item.created_at)
      .map((item) => ({
        id: item.id,
        type: mapActionToActivityType(item.action),
        description: formatActivityDescription(item.action, item.changes),
        timestamp: item.created_at as string,
      }));
  } catch (error) {
    logger.error(`Error getting recent activity: ${error}`);
    return [];
  }
}

/**
 * Map database action to activity type
 */
function mapActionToActivityType(action: string): string {
  if (action.includes("status")) return "status";
  if (action.includes("contact")) return "contact";
  if (action.includes("document")) return "document";
  if (action.includes("appointment")) return "appointment";
  return "note";
}

/**
 * Format activity description from action and changes
 */
function formatActivityDescription(action: string, changes: any): string {
  if (action.includes("status_changed")) {
    const from = changes?.from_status || "unknown";
    const to = changes?.to_status || "unknown";
    return `Status changed from ${from} to ${to}`;
  }

  return action.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
}
