import type { CaseFileWithDetails } from "../types";
import { createSupabaseClient } from "../../shared/database/supabaseClient";
import { getCaseFile } from "./getCaseFile";
import { getEmployeeOptional } from "../../employee/queries/getEmployee";
import { getContactsByCaseFile } from "../../contact/queries/getContact";
import { toCaseFileWithDetails } from "../transformers/toCaseFileWithDetails";

/**
 * Get case file with full details including employee and contacts
 * @param id Case file ID
 * @returns Case file with all related data
 */
export const getCaseFileWithDetails = async (id: string): Promise<CaseFileWithDetails> => {
  const supabase = await createSupabaseClient();

  // Get core case file data
  const caseFile = await getCaseFile(supabase, id);

  // Get related data in parallel
  const [employee, contacts] = await Promise.all([
    caseFile.assigned_to ? getEmployeeOptional(supabase, caseFile.assigned_to) : null,
    getContactsByCaseFile(supabase, id),
  ]);

  // Transform to domain model
  return toCaseFileWithDetails({
    caseFile,
    employee,
    contacts,
  });
};

/**
 * Get case file with details and specific includes
 * @param id Case file ID
 * @param options What to include in the response
 * @returns Case file with requested related data
 */
export const getCaseFileWithDetailsOptional = async (
  id: string,
  options: {
    includeEmployee?: boolean;
    includeContacts?: boolean;
  } = {}
): Promise<CaseFileWithDetails> => {
  const { includeEmployee = true, includeContacts = true } = options;

  const supabase = await createSupabaseClient();

  // Get core case file data
  const caseFile = await getCaseFile(supabase, id);

  // Get related data based on options
  const [employee, contacts] = await Promise.all([
    includeEmployee && caseFile.assigned_to
      ? getEmployeeOptional(supabase, caseFile.assigned_to)
      : null,
    includeContacts ? getContactsByCaseFile(supabase, id) : [],
  ]);

  // Transform to domain model
  return toCaseFileWithDetails({
    caseFile,
    employee,
    contacts,
  });
};
