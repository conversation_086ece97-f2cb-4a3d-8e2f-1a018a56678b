"use server";

import { createSupabaseClient } from "../../shared/database/supabaseClient";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Get case file statistics by status
 * @returns Statistics object with counts by status
 */
export async function getCaseFileStatistics(): Promise<{
  opening: number;
  active: number;
  suspended: number;
  closed: number;
  total: number;
}> {
  const supabase = await createSupabaseClient();

  try {
    // Get counts by status
    const { data, error } = await supabase
      .from("case_files")
      .select("status")
      .not("status", "is", null);

    if (error) {
      logger.error(`Error getting case file statistics: ${error.message}`);
      throw new Error("Failed to get case file statistics");
    }

    // Count by status
    const stats = {
      opening: 0,
      active: 0,
      suspended: 0,
      closed: 0,
      total: 0,
    };

    data.forEach((caseFile) => {
      const status = caseFile.status as keyof typeof stats;
      if (status in stats && status !== "total") {
        stats[status]++;
      }
      stats.total++;
    });

    return stats;
  } catch (error) {
    logger.error(`<PERSON>rror getting case file statistics: ${error}`);
    throw error;
  }
}
