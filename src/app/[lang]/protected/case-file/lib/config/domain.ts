/**
 * Domain Configuration
 *
 * This file defines the domain-level configuration including:
 * - Domain name
 * - Base path
 * - Features list
 * - Domain-level permissions
 *
 * This configuration is used throughout the domain to ensure consistency
 * and make it easier to update paths, names, and permissions in one place.
 */

// Domain identifier (used in permissions, routes, etc.)
export const DOMAIN_ID = "case-file";

// Domain display name (used in UI)
export const DOMAIN_NAME = "Case File";

// Base path for all routes in this domain
export const DOMAIN_BASE_PATH = "/protected/case-file";

// Domain description (used in UI)
export const DOMAIN_DESCRIPTION = "Case file management and state tracking";

// Domain permissions
export const DOMAIN_PERMISSIONS = {
  VIEW: `${DOMAIN_ID}:view`,
  CREATE: `${DOMAIN_ID}:create`,
  UPDATE: `${DOMAIN_ID}:update`,
  DELETE: `${DOMAIN_ID}:delete`,
  ACTIVATE: `${DOMAIN_ID}:activate`,
  SUSPEND: `${DOMAIN_ID}:suspend`,
  CLOSE: `${DOMAIN_ID}:close`,
  ADMIN: `${DOMAIN_ID}:admin`,
};

// Domain configuration object
const DOMAIN_CONFIG = {
  id: DOMAIN_ID,
  name: DOMAIN_NAME,
  basePath: DOMAIN_BASE_PATH,
  description: DOMAIN_DESCRIPTION,
  permissions: DOMAIN_PERMISSIONS,
};

export default DOMAIN_CONFIG;
