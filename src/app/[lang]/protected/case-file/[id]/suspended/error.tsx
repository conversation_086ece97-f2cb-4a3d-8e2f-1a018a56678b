"use client";

import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";
import { H1, P } from "@/components/typography";

interface OpeningCaseFileErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function OpeningCaseFileError({ error, reset }: OpeningCaseFileErrorProps) {
  useEffect(() => {
    console.error("Opening Case File Error:", error);
  }, [error]);

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <div className="bg-red-100 p-2 rounded-full mr-3">
          <AlertCircle className="h-10 w-10 text-red-600" />
        </div>
        <div>
          <H1>Error Loading Opening Case File</H1>
          <P className="mt-1 text-muted-foreground">
            Something went wrong while loading the opening case file
          </P>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Unable to Load Case File</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <P className="text-muted-foreground">
            We encountered an error while trying to load the case file information.
          </P>

          <div className="flex gap-4">
            <Button onClick={reset} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            <Button variant="outline" onClick={() => window.history.back()}>
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
