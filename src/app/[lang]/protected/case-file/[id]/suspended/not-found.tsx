import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileX, Home, ArrowLeft } from "lucide-react";
import { H1, P } from "@/components/typography";
import Link from "next/link";

export default function OpeningCaseFileNotFound() {
  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <div className="bg-gray-100 p-2 rounded-full mr-3">
          <FileX className="h-10 w-10 text-gray-600" />
        </div>
        <div>
          <H1>Opening Case File Not Found</H1>
          <P className="mt-1 text-muted-foreground">
            The requested opening case file could not be found
          </P>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Case File Not Available</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <P className="text-muted-foreground">
            The opening case file you're looking for doesn't exist or you don't have permission to
            access it.
          </P>

          <div className="flex gap-4">
            <Button asChild>
              <Link href="/protected/case-file" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Case Files
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/protected/dashboard" className="flex items-center gap-2">
                <Home className="h-4 w-4" />
                Dashboard
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
