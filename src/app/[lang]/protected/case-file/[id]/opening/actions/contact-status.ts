"use server";

import { DocumentService } from "../lib/documentService";
import { ContactStatus } from "../types/ContactStatus";

/**
 * Calculate document completion status for all contacts in a case file
 */
export async function getContactDocumentStatuses(
  caseFileId: string,
  contacts: any[]
): Promise<ContactStatus[]> {
  const statuses: ContactStatus[] = [];

  for (const contact of contacts) {
    try {
      const documents = await DocumentService.getDocumentsForContact(caseFileId, contact.id);

      if (documents.length === 0) {
        statuses.push({
          contactId: contact.id,
          contactName: `${contact.first_name} ${contact.last_name}`,
          isComplete: true,
          reason: "No documents required",
          documentCount: 0,
        });
      } else {
        const completedDocs = documents.filter(
          (doc) => DocumentService.getDocumentStatus(doc) === "completed"
        );
        const allCompleted = completedDocs.length === documents.length;

        statuses.push({
          contactId: contact.id,
          contactName: `${contact.first_name} ${contact.last_name}`,
          isComplete: allCompleted,
          reason: allCompleted
            ? `All ${documents.length} documents completed`
            : `${documents.length - completedDocs.length} of ${documents.length} documents incomplete`,
          documentCount: documents.length,
        });
      }
    } catch (error) {
      console.error(`Error calculating status for contact ${contact.id}:`, error);
      statuses.push({
        contactId: contact.id,
        contactName: `${contact.first_name} ${contact.last_name}`,
        isComplete: false,
        reason: "Error loading documents",
        documentCount: 0,
      });
    }
  }

  return statuses;
}
