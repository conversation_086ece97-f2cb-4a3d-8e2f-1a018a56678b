"use client";

import { motion } from "framer-motion";
import { ContactCard } from "./ContactCard";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface ContactListProps {
  contacts: Contact[];
  caseFileId: string;
  lang: string;
  dictionary: Dictionary;
}

/**
 * Contact list component with staggered animations
 * Displays contacts as clickable cards
 */
export function ContactList({ contacts, caseFileId, lang, dictionary }: ContactListProps) {
  if (!contacts || contacts.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.0, ease: "easeOut" }}
        className="text-center py-8"
      >
        <div className="text-muted-foreground">
          <p className="text-sm">{dictionary.caseFileOpening.contacts.noContacts}</p>
          <p className="text-xs mt-1">
            {dictionary.caseFileOpening.contacts.noContactsDescription}
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="space-y-3">
      {contacts.map((contact, index) => (
        <motion.div
          key={contact.id}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: index * 0.2,
            duration: 0.8,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        >
          <ContactCard contact={contact} caseFileId={caseFileId} lang={lang} />
        </motion.div>
      ))}
    </div>
  );
}
