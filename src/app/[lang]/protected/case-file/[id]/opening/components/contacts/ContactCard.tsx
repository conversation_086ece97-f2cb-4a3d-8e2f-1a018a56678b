"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { P } from "@/components/typography";
import { ContactAvatar } from "./ContactAvatar";
import { Mail, Phone } from "lucide-react";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface ContactCardProps {
  contact: Contact;
  caseFileId: string;
  lang: string;
}

/**
 * Individual contact card component
 * Displays contact information with hover effects
 */
export function ContactCard({ contact, caseFileId, lang }: ContactCardProps) {
  // Extract email and phone from JSONB fields
  const getEmail = () => {
    if (!contact.email) return null;
    if (typeof contact.email === "string") return contact.email;
    return contact.email.personal || contact.email.work || null;
  };

  const getPhone = () => {
    if (!contact.phone) return null;
    if (typeof contact.phone === "string") return contact.phone;
    return contact.phone.mobile || contact.phone.home || null;
  };

  const email = getEmail();
  const phone = getPhone();

  return (
    <Link href={`/${lang}/protected/case-file/${caseFileId}/opening/contacts/${contact.id}`}>
      <motion.div
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 200, damping: 15 }}
        className="group"
      >
        <Card className="cursor-pointer hover:shadow-lg transition-all duration-300 group-hover:border-blue-200 group-hover:bg-blue-50/50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {/* Avatar */}
              <ContactAvatar name={contact.name} />

              {/* Contact Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <P className="font-medium truncate">{contact.name}</P>
                    <Badge variant="secondary" className="text-xs mt-1">
                      {contact.relationship_type}
                    </Badge>
                  </div>
                </div>

                {/* Contact Details */}
                <div className="mt-2 space-y-1">
                  {email && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Mail className="h-3 w-3" />
                      <span className="truncate">{email}</span>
                    </div>
                  )}
                  {phone && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Phone className="h-3 w-3" />
                      <span>{phone}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </Link>
  );
}
