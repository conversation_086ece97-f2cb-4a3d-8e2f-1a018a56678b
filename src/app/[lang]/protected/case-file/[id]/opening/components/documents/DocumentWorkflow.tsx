"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { FileText } from "lucide-react";
import { EmptyDocumentState } from "./EmptyDocumentState";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface DocumentWorkflowProps {
  caseFileId: string;
  dictionary: Dictionary;
}

/**
 * Document workflow component
 * Manages document display and actions for selected contacts
 */
export function DocumentWorkflow({ caseFileId: _caseFileId, dictionary }: DocumentWorkflowProps) {
  const [selectedContactId, _setSelectedContactId] = useState<string | null>(null);

  // For now, show empty state until contact selection is implemented
  if (!selectedContactId) {
    return <EmptyDocumentState dictionary={dictionary} />;
  }

  // TODO: Implement document list for selected contact
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <H2>Contact Documents</H2>
            <P className="text-muted-foreground">
              Documents for the selected contact will appear here.
            </P>

            {/* TODO: Implement DocumentList component */}
            <div className="text-center py-8 text-muted-foreground">
              <P className="text-sm">Document list implementation coming soon...</P>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
