"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface PageTransitionProps {
  children: ReactNode;
  direction?: "forward" | "backward";
}

/**
 * Page transition wrapper for smooth navigation between wizard steps
 * Creates a seamless flow between different routes
 */
export function PageTransition({ children, direction = "forward" }: PageTransitionProps) {
  const variants = {
    initial: {
      opacity: 0,
      x: direction === "forward" ? 50 : -50,
      scale: 0.98,
    },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
    },
    exit: {
      opacity: 0,
      x: direction === "forward" ? -50 : 50,
      scale: 0.98,
    },
  };

  return (
    <motion.div
      variants={variants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
      }}
      className="w-full"
    >
      {children}
    </motion.div>
  );
}
