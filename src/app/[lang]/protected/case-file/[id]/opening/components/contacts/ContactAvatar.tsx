"use client";

import { motion } from "framer-motion";

interface ContactAvatarProps {
  name: string;
  size?: "sm" | "md" | "lg";
}

/**
 * Contact avatar component
 * Displays initials in a colored circle
 */
export function ContactAvatar({ name, size = "md" }: ContactAvatarProps) {
  // Generate initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate consistent color based on name
  const getAvatarColor = (name: string) => {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-orange-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-teal-500",
      "bg-red-500",
    ];

    const hash = name.split("").reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    return colors[Math.abs(hash) % colors.length];
  };

  const sizeClasses = {
    sm: "h-8 w-8 text-xs",
    md: "h-10 w-10 text-sm",
    lg: "h-12 w-12 text-base",
  };

  const initials = getInitials(name);
  const colorClass = getAvatarColor(name);

  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={`
        ${sizeClasses[size]} 
        ${colorClass}
        rounded-full 
        flex 
        items-center 
        justify-center 
        text-white 
        font-medium
        flex-shrink-0
      `}
    >
      {initials}
    </motion.div>
  );
}
