"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { H3, P } from "@/components/typography";
import {
  CheckCircle,
  Circle,
  Users,
  FileText,
  Play,
  AlertCircle,
  Clock,
  Target,
} from "lucide-react";
import { ActivationForm } from "./ActivationForm";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface CaseFileActivationPanelProps {
  caseFileId: string;
  dictionary: Dictionary;
  contacts: any[];
}

interface RequirementItem {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  icon: any;
}

/**
 * Case File Activation Panel
 * Shows progress overview and activation controls when no contact is selected
 */
export function CaseFileActivationPanel({
  caseFileId,
  dictionary,
  contacts,
}: CaseFileActivationPanelProps) {
  const [showActivationForm, setShowActivationForm] = useState(false);

  // Calculate requirements based on current state
  const requirements: RequirementItem[] = [
    {
      id: "contacts",
      title: "Family Contacts",
      description: "At least one family contact must be added",
      completed: contacts && contacts.length > 0,
      required: true,
      icon: Users,
    },
    {
      id: "documents",
      title: "Required Documents",
      description: "All mandatory documents must be uploaded and signed",
      completed: false, // TODO: Calculate based on actual document status
      required: true,
      icon: FileText,
    },
    {
      id: "verification",
      title: "Information Verification",
      description: "Contact information and service requirements verified",
      completed: contacts && contacts.length > 0, // Simplified for now
      required: true,
      icon: CheckCircle,
    },
    {
      id: "assessment",
      title: "Initial Assessment",
      description: "Preliminary assessment completed (optional)",
      completed: false,
      required: false,
      icon: Target,
    },
  ];

  const completedRequirements = requirements.filter((req) => req.completed).length;
  const totalRequirements = requirements.length;
  const requiredCompleted = requirements
    .filter((req) => req.required)
    .every((req) => req.completed);
  const progressPercentage = (completedRequirements / totalRequirements) * 100;

  const getRequirementIcon = (requirement: RequirementItem) => {
    const IconComponent = requirement.icon;
    if (requirement.completed) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <Circle className="h-5 w-5 text-gray-300" />;
  };

  const getRequirementBadge = (requirement: RequirementItem) => {
    if (requirement.completed) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          Complete
        </Badge>
      );
    }
    if (requirement.required) {
      return <Badge variant="destructive">Required</Badge>;
    }
    return <Badge variant="secondary">Optional</Badge>;
  };

  return (
    <>
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Case File Activation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between">
              <H3>Progress Overview</H3>
              <Badge variant={requiredCompleted ? "default" : "secondary"}>
                {completedRequirements}/{totalRequirements} Complete
              </Badge>
            </div>

            <Progress value={progressPercentage} className="w-full" />

            <P className="text-sm text-muted-foreground">
              {requiredCompleted
                ? "All required items completed. Ready to activate!"
                : "Complete all required items to activate this case file."}
            </P>
          </motion.div>

          {/* Requirements Checklist */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6, ease: "easeOut" }}
            className="space-y-4"
          >
            <H3>Activation Requirements</H3>
            <div className="space-y-3">
              {requirements.map((requirement, index) => (
                <motion.div
                  key={requirement.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
                  className="flex items-start gap-3 p-3 rounded-lg border bg-card"
                >
                  {getRequirementIcon(requirement)}
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <P className="font-medium">{requirement.title}</P>
                      {getRequirementBadge(requirement)}
                    </div>
                    <P className="text-sm text-muted-foreground">{requirement.description}</P>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Status Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6, ease: "easeOut" }}
            className="p-4 rounded-lg bg-muted/50 space-y-2"
          >
            <div className="flex items-center gap-2">
              {requiredCompleted ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <Clock className="h-5 w-5 text-orange-500" />
              )}
              <P className="font-medium">
                {requiredCompleted ? "Ready for Activation" : "Pending Requirements"}
              </P>
            </div>
            <P className="text-sm text-muted-foreground">
              {requiredCompleted
                ? "This case file meets all requirements and can be activated to begin active service delivery."
                : "Complete the required items above to enable case file activation."}
            </P>
          </motion.div>

          {/* Activation Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6, ease: "easeOut" }}
            className="pt-4"
          >
            <Button
              onClick={() => setShowActivationForm(true)}
              disabled={!requiredCompleted}
              className="w-full"
              size="lg"
            >
              <Play className="h-4 w-4 mr-2" />
              {requiredCompleted ? "Activate Case File" : "Complete Requirements First"}
            </Button>

            {!requiredCompleted && (
              <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                <AlertCircle className="h-4 w-4" />
                <span>All required items must be completed before activation</span>
              </div>
            )}
          </motion.div>
        </CardContent>
      </Card>

      {/* Activation Form Modal */}
      {showActivationForm && (
        <ActivationForm
          caseFileId={caseFileId}
          onClose={() => setShowActivationForm(false)}
          dictionary={dictionary}
        />
      )}
    </>
  );
}
