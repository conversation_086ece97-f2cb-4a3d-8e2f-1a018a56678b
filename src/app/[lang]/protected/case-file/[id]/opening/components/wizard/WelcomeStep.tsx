"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { H2, P } from "@/components/typography";
import { FileText, Users, CheckCircle, ArrowRight } from "lucide-react";
import Link from "next/link";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface WelcomeStepProps {
  caseFileId: string;
  lang: string;
  dictionary: Dictionary;
}

/**
 * Welcome step component for the opening case file wizard
 * Introduces the document workflow and provides call-to-action
 */
export function WelcomeStep({ caseFileId, lang, dictionary }: WelcomeStepProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 1.2, ease: [0.25, 0.46, 0.45, 0.94] }}
      className="max-w-2xl mx-auto"
    >
      <Card className="text-center">
        <CardContent className="space-y-6 p-8">
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.6, duration: 0.8, type: "spring", stiffness: 100 }}
            className="flex justify-center"
          >
            <div className="bg-blue-50 p-4 rounded-full">
              <FileText className="h-12 w-12 text-blue-600" />
            </div>
          </motion.div>

          {/* Title */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.0, duration: 0.8, ease: "easeOut" }}
          >
            <H2>{dictionary.caseFileOpening.wizard.welcome.title}</H2>
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.4, duration: 0.8, ease: "easeOut" }}
            className="space-y-4"
          >
            <P>{dictionary.caseFileOpening.wizard.welcome.description}</P>

            {/* Features List */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.8, duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="flex items-start gap-3"
              >
                <Users className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">
                    {dictionary.caseFileOpening.wizard.welcome.features.contacts.title}
                  </P>
                  <P className="text-sm text-muted-foreground">
                    {dictionary.caseFileOpening.wizard.welcome.features.contacts.description}
                  </P>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 2.1, duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="flex items-start gap-3"
              >
                <FileText className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">
                    {dictionary.caseFileOpening.wizard.welcome.features.documents.title}
                  </P>
                  <P className="text-sm text-muted-foreground">
                    {dictionary.caseFileOpening.wizard.welcome.features.documents.description}
                  </P>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 2.4, duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="flex items-start gap-3"
              >
                <CheckCircle className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">
                    {dictionary.caseFileOpening.wizard.welcome.features.tracking.title}
                  </P>
                  <P className="text-sm text-muted-foreground">
                    {dictionary.caseFileOpening.wizard.welcome.features.tracking.description}
                  </P>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2.8, duration: 1.0, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <Link href={`/${lang}/protected/case-file/${caseFileId}/opening/contacts`}>
              <Button size="lg" className="group">
                {dictionary.caseFileOpening.wizard.welcome.getStarted}
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
