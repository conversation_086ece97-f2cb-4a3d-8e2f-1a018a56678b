"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { ContactList } from "../contacts/ContactList";
import { EmptyDocumentState } from "../documents/EmptyDocumentState";
import { CaseFileWithRelations } from "../../../../lib/types";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface ContactsRevealStepProps {
  caseFile: CaseFileWithRelations;
  lang: string;
  dictionary: Dictionary;
}

/**
 * Contacts reveal step component
 * Shows the contact list with fade-in animation and empty document state
 */
export function ContactsRevealStep({ caseFile, lang, dictionary }: ContactsRevealStepProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left Sidebar - Contact List */}
      <motion.div
        initial={{ opacity: 0, x: -60 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-1"
      >
        <Card>
          <CardContent className="p-6">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 1.0, ease: "easeOut" }}
              className="space-y-4"
            >
              <H2>{dictionary.caseFileOpening.contacts.title}</H2>
              <P className="text-sm text-muted-foreground">
                {dictionary.caseFileOpening.contacts.description}
              </P>

              <ContactList
                contacts={caseFile.contacts || []}
                caseFileId={caseFile.id}
                lang={lang}
                dictionary={dictionary}
              />
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Right Main Content - Empty State */}
      <motion.div
        initial={{ opacity: 0, x: 60 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1.2, duration: 1.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-2"
      >
        <EmptyDocumentState dictionary={dictionary} />
      </motion.div>
    </div>
  );
}
