"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { H2, H3, P } from "@/components/typography";
import { ContactAvatar } from "../../../components/contacts/ContactAvatar";
import { FileText, Eye, CheckCircle, Clock, AlertCircle, ExternalLink } from "lucide-react";
import { CaseFileWithRelations } from "../../../../../lib/types";
import { DocumentWithSignature } from "../../../lib/documentService";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface DocumentWithStatus extends DocumentWithSignature {
  status: string;
  requiresAction: boolean;
}

interface ContactDocumentWorkflowProps {
  contact: Contact;
  caseFile: CaseFileWithRelations;
  lang: string;
  documents: DocumentWithStatus[];
  dictionary: Dictionary;
}

/**
 * Contact-specific document workflow component
 * Shows documents and required actions for a selected contact
 */
export function ContactDocumentWorkflow({
  contact,
  caseFile,
  lang,
  documents,
  dictionary,
}: ContactDocumentWorkflowProps) {
  const getStatusBadge = (document: DocumentWithStatus) => {
    const status = document.status;

    switch (status) {
      case "completed":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            {dictionary.caseFileOpening.documents.status.completed}
          </Badge>
        );
      case "pending_signature":
        return (
          <Badge variant="default" className="bg-orange-100 text-orange-800 border-orange-200">
            <Clock className="h-3 w-3 mr-1" />
            {dictionary.caseFileOpening.documents.status.needsSignature}
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="default" className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            {dictionary.caseFileOpening.documents.status.rejected}
          </Badge>
        );
      case "expired":
        return (
          <Badge variant="default" className="bg-gray-100 text-gray-800 border-gray-200">
            <Clock className="h-3 w-3 mr-1" />
            {dictionary.caseFileOpening.documents.status.expired}
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Eye className="h-3 w-3 mr-1" />
            {dictionary.caseFileOpening.documents.status.review}
          </Badge>
        );
    }
  };

  const getActionButton = (document: DocumentWithStatus) => {
    const status = document.status;
    const requiresAction = document.requiresAction;

    return (
      <Link href={`/${lang}/protected/document/attachments/${document.id}/view`}>
        <Button
          size="sm"
          variant={requiresAction ? "default" : "outline"}
          className="flex items-center gap-2"
        >
          {status === "completed" ? (
            <>
              <Eye className="h-4 w-4" />
              {dictionary.caseFileOpening.documents.actions.viewDocument}
            </>
          ) : requiresAction ? (
            <>
              <FileText className="h-4 w-4" />
              {dictionary.caseFileOpening.documents.actions.signDocument}
            </>
          ) : (
            <>
              <Eye className="h-4 w-4" />
              {dictionary.caseFileOpening.documents.actions.viewDocument}
            </>
          )}
          <ExternalLink className="h-3 w-3" />
        </Button>
      </Link>
    );
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Sidebar - Contact Info */}
      <motion.div
        initial={{ opacity: 0, x: -40 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.2, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-1"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <ContactAvatar name={contact.name} />
              <div>
                <H3>{contact.name}</H3>
                <Badge variant="secondary" className="text-xs">
                  {contact.relationship_type}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <P className="text-sm font-medium">Case File</P>
                <P className="text-sm text-muted-foreground">{caseFile.case_number}</P>
              </div>
              {contact.email && (
                <div>
                  <P className="text-sm font-medium">Email</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.email === "string"
                      ? contact.email
                      : contact.email.personal || contact.email.work}
                  </P>
                </div>
              )}
              {contact.phone && (
                <div>
                  <P className="text-sm font-medium">Phone</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.phone === "string"
                      ? contact.phone
                      : contact.phone.mobile || contact.phone.home}
                  </P>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Right Main Content - Documents */}
      <motion.div
        initial={{ opacity: 0, x: 40 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.2, delay: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-3"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {dictionary.caseFileOpening.documents.title.replace("{contactName}", contact.name)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <H2>
                {dictionary.caseFileOpening.documents.title.replace("{contactName}", contact.name)}
              </H2>

              {documents.length === 0 ? (
                <Card className="border-l-4 border-l-gray-200">
                  <CardContent className="p-6 text-center">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <H3 className="text-gray-600 mb-2">
                      {dictionary.caseFileOpening.documents.noDocuments}
                    </H3>
                    <P className="text-sm text-muted-foreground">
                      {dictionary.caseFileOpening.documents.noDocumentsDescription}
                    </P>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {documents.map((document, index) => (
                    <motion.div
                      key={document.id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.8,
                        delay: 0.8 + index * 0.2,
                        ease: [0.25, 0.46, 0.45, 0.94],
                      }}
                    >
                      <Card className="border-l-4 border-l-blue-200 hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <H3>{document.document_name}</H3>
                                {getStatusBadge(document)}
                                {document.signature_required && (
                                  <Badge variant="outline" className="text-xs">
                                    {dictionary.caseFileOpening.documents.badges.signatureRequired}
                                  </Badge>
                                )}
                              </div>
                              <P className="text-sm text-muted-foreground mb-2">
                                {document.document_type} •{" "}
                                {document.file_size
                                  ? `${Math.round(document.file_size / 1024)} KB`
                                  : "Unknown size"}
                              </P>
                              {document.signed_at && (
                                <P className="text-xs text-green-600">
                                  {dictionary.caseFileOpening.documents.signedOn.replace(
                                    "{date}",
                                    new Date(document.signed_at).toLocaleDateString()
                                  )}
                                </P>
                              )}
                            </div>
                            <div className="ml-4">{getActionButton(document)}</div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              )}

              {/* Progress Summary */}
              {documents.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1.0, delay: 1.6, ease: [0.25, 0.46, 0.45, 0.94] }}
                  className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <P className="font-medium">
                        {dictionary.caseFileOpening.documents.progressSummary}
                      </P>
                      <P className="text-sm text-muted-foreground">
                        {dictionary.caseFileOpening.documents.progressDescription
                          .replace(
                            "{completed}",
                            documents.filter((d) => d.status === "completed").length.toString()
                          )
                          .replace(
                            "{total}",
                            documents.filter((d) => d.signature_required).length.toString()
                          )}
                      </P>
                    </div>
                    <div className="text-right">
                      <P className="text-2xl font-bold text-blue-600">
                        {documents.filter((d) => d.signature_required).length > 0
                          ? Math.round(
                              (documents.filter((d) => d.status === "completed").length /
                                documents.filter((d) => d.signature_required).length) *
                                100
                            )
                          : 100}
                        %
                      </P>
                      <P className="text-xs text-muted-foreground">
                        {dictionary.caseFileOpening.documents.complete}
                      </P>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
