import { notFound } from "next/navigation";
import Link from "next/link";
import { getCaseFileWithRelations } from "../../../actions";
import { ContactsRevealStep } from "../components/wizard/ContactsRevealStep";
import { EnhancedPageHeader } from "../components/EnhancedPageHeader";
import { PageTransition } from "../components/PageTransition";
import { ArrowRight } from "lucide-react";
import { getDictionary } from "@/lib/i18n/cache";

interface ContactsPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Contacts reveal step of the opening case file wizard
 * Second step that shows the contact list with fade-in animation
 */
export default async function ContactsPage({ params }: ContactsPageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Get dictionary for translations
  const dictionary = await getDictionary();

  return (
    <PageTransition direction="forward">
      <div className="space-y-6">
        {/* Enhanced Page Header */}
        <EnhancedPageHeader caseFile={caseFile} />

        {/* Contacts Reveal Step */}
        <ContactsRevealStep caseFile={caseFile} lang={lang} dictionary={dictionary} />

        {/* Continue Button */}
        <div className="flex justify-center pt-6">
          <Link
            href={`/${lang}/protected/case-file/${id}/opening/documents`}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {dictionary.caseFileOpening.wizard.contacts.continueToDocuments}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
    </PageTransition>
  );
}
