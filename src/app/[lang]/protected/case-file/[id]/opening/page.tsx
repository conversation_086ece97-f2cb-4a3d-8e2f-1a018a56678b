import { notFound, redirect } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { checkWizardStatus } from "./lib/wizardService";
import { getContactDocumentStatuses } from "./actions/contact-status";
import { EnhancedPageHeader } from "./components/EnhancedPageHeader";
import { DocumentWorkflow } from "./components/documents/DocumentWorkflow";
import { ContactList } from "./components/contacts/ContactList";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { getDictionary } from "@/lib/i18n/cache";

interface OpeningCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Main Opening Case File Page
 * Simple implementation for now
 */
export default async function OpeningCaseFilePage({ params }: OpeningCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Get dictionary for translations
  const dictionary = await getDictionary();

  // Check wizard status
  const wizardCompleted = await checkWizardStatus(id);

  // If wizard not completed, redirect to welcome step
  if (!wizardCompleted) {
    redirect(`/${lang}/protected/case-file/${id}/opening/welcome`);
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Page Header */}
      <EnhancedPageHeader caseFile={caseFile} />

      {/* Main Document Workflow Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Sidebar - Contact List */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <H2>{dictionary.caseFileOpening.contacts.title}</H2>
                <P className="text-sm text-muted-foreground">
                  {dictionary.caseFileOpening.contacts.description}
                </P>

                <ContactList
                  contacts={caseFile.contacts || []}
                  caseFileId={caseFile.id}
                  lang={lang}
                  dictionary={dictionary}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Main Content - Document Workflow */}
        <div className="lg:col-span-2">
          <DocumentWorkflow
            caseFileId={caseFile.id}
            dictionary={dictionary}
            contacts={caseFile.contacts || []}
          />
        </div>
      </div>
    </div>
  );
}
