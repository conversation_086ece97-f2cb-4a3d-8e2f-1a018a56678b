import { notFound } from "next/navigation";
import { getCaseFileDashboardData, getCaseFileContacts } from "../../actions";
import { CaseFileDashboard } from "../../components";
import { CaseFileNavigation } from "../../components/navigation";
import { PageHeader } from "@/components/PageHeader";
import { Button } from "@/components/ui/button";
import { Settings, Users, Calendar } from "lucide-react";
import { CaseFileDocuments } from "./components/CaseFileDocuments";
import { CaseFileHistory } from "./components/CaseFileHistory";
import { CaseFileContacts } from "./components/CaseFileContacts";
import { getDictionary } from "@/lib/i18n/cache";

interface ActiveCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Active Case File Management Page
 * Central hub for managing ongoing family services
 */
export default async function ActiveCaseFilePage({ params }: ActiveCaseFilePageProps) {
  const { lang, id } = await params;

  // Get dictionary for translations
  const dictionary = await getDictionary();
  const t = dictionary.caseFile.active;

  // Get dashboard data using server action
  const dashboardResponse = await getCaseFileDashboardData(id);

  if (!dashboardResponse.success || !dashboardResponse.data) {
    notFound();
  }

  const { caseFile } = dashboardResponse.data;

  // Get case file contacts
  const contactsResponse = await getCaseFileContacts(id);
  const contacts = contactsResponse.success ? contactsResponse.data || [] : [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`${t.title} ${caseFile.case_number}`}
        description={t.description}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Family
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              {t.dashboard.quickActions.schedule}
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Main Dashboard */}
      <CaseFileDashboard data={dashboardResponse.data} lang={lang} />

      {/* Document Management */}
      <CaseFileDocuments caseFileId={caseFile.id} lang={lang} dictionary={t.documents} />

      {/* Contacts */}
      <CaseFileContacts
        contacts={contacts}
        caseFileId={caseFile.id}
        lang={lang}
        dictionary={t.contacts}
      />

      {/* Case File History */}
      <CaseFileHistory caseFileId={caseFile.id} lang={lang} />
    </div>
  );
}
