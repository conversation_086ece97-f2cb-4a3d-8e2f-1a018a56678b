import { notFound } from "next/navigation";
import { getCaseFileDashboardData } from "../../actions";
import { CaseFileDashboard } from "../../components";
import { CaseFileNavigation } from "../../components/navigation";
import { PageHeader } from "@/components/PageHeader";
import { Button } from "@/components/ui/button";
import { Settings, Users, Calendar } from "lucide-react";
import { CaseFileDocuments } from "./components/CaseFileDocuments";

interface ActiveCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Active Case File Management Page
 * Central hub for managing ongoing family services
 */
export default async function ActiveCaseFilePage({ params }: ActiveCaseFilePageProps) {
  const { lang, id } = await params;

  // Get dashboard data using server action
  const dashboardResponse = await getCaseFileDashboardData(id);

  if (!dashboardResponse.success || !dashboardResponse.data) {
    notFound();
  }

  const { caseFile } = dashboardResponse.data;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`Case File ${caseFile.case_number}`}
        description="Active case file management and monitoring"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Family
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Main Dashboard */}
      <CaseFileDashboard data={dashboardResponse.data} lang={lang} />
    </div>
  );
}
