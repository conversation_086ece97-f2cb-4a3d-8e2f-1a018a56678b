"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { createClient } from "@/lib/supabase/server";

/**
 * Add a contact to a case file
 */
export const addContactToCaseFile = requirePermission("case_file:update")(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const caseFileId = formData.get("caseFileId") as string;
    const contactId = formData.get("contactId") as string;
    const relationshipType = formData.get("relationshipType") as string;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!caseFileId) {
      return errorActionState("Case file ID is required");
    }

    if (!contactId) {
      return errorActionState("Contact ID is required");
    }

    if (!relationshipType) {
      return errorActionState("Relationship type is required");
    }

    const supabase = await createClient();

    // Get the case file to get organization_id
    const { data: caseFile, error: caseFileError } = await supabase
      .from("case_files")
      .select("organization_id")
      .eq("id", caseFileId)
      .single();

    if (caseFileError || !caseFile) {
      logger.error(`Error getting case file: ${caseFileError?.message}`);
      return errorActionState("Case file not found");
    }

    // Check if the relationship already exists
    const { data: existingRelation, error: checkError } = await supabase
      .from("case_file_contacts")
      .select("id")
      .eq("case_file_id", caseFileId)
      .eq("contact_id", contactId)
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      logger.error(`Error checking existing relationship: ${checkError.message}`);
      return errorActionState("Failed to check existing relationship");
    }

    if (existingRelation) {
      return errorActionState("Contact is already associated with this case file");
    }

    // Add the contact to the case file
    const { error: insertError } = await supabase.from("case_file_contacts").insert({
      case_file_id: caseFileId,
      contact_id: contactId,
      relationship_type: relationshipType,
      organization_id: caseFile.organization_id,
    });

    if (insertError) {
      logger.error(`Error adding contact to case file: ${insertError.message}`);
      return errorActionState("Failed to add contact to case file");
    }

    // Revalidate the active case file page
    revalidatePath(`/${lang}/protected/case-file/${caseFileId}/active`);

    logger.info(`Contact ${contactId} added to case file ${caseFileId}`);
    return successActionState("Contact added successfully");
  } catch (error) {
    logger.error(`Unexpected error adding contact to case file: ${error}`);
    return errorActionState("An unexpected error occurred");
  }
});

/**
 * Remove a contact from a case file
 */
export const removeContactFromCaseFile = requirePermission("case_file:update")(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const caseFileId = formData.get("caseFileId") as string;
    const contactId = formData.get("contactId") as string;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!caseFileId) {
      return errorActionState("Case file ID is required");
    }

    if (!contactId) {
      return errorActionState("Contact ID is required");
    }

    const supabase = await createClient();

    // Remove the contact from the case file
    const { error: deleteError } = await supabase
      .from("case_file_contacts")
      .delete()
      .eq("case_file_id", caseFileId)
      .eq("contact_id", contactId);

    if (deleteError) {
      logger.error(`Error removing contact from case file: ${deleteError.message}`);
      return errorActionState("Failed to remove contact from case file");
    }

    // Revalidate the active case file page
    revalidatePath(`/${lang}/protected/case-file/${caseFileId}/active`);

    logger.info(`Contact ${contactId} removed from case file ${caseFileId}`);
    return successActionState("Contact removed successfully");
  } catch (error) {
    logger.error(`Unexpected error removing contact from case file: ${error}`);
    return errorActionState("An unexpected error occurred");
  }
});

/**
 * Update contact relationship type in case file
 */
export const updateContactRelationship = requirePermission("case_file:update")(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const caseFileId = formData.get("caseFileId") as string;
    const contactId = formData.get("contactId") as string;
    const relationshipType = formData.get("relationshipType") as string;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!caseFileId || !contactId || !relationshipType) {
      return errorActionState("All fields are required");
    }

    const supabase = await createClient();

    // Update the relationship type
    const { error: updateError } = await supabase
      .from("case_file_contacts")
      .update({
        relationship_type: relationshipType,
      })
      .eq("case_file_id", caseFileId)
      .eq("contact_id", contactId);

    if (updateError) {
      logger.error(`Error updating contact relationship: ${updateError.message}`);
      return errorActionState("Failed to update contact relationship");
    }

    // Revalidate the active case file page
    revalidatePath(`/${lang}/protected/case-file/${caseFileId}/active`);

    logger.info(`Contact ${contactId} relationship updated in case file ${caseFileId}`);
    return successActionState("Contact relationship updated successfully");
  } catch (error) {
    logger.error(`Unexpected error updating contact relationship: ${error}`);
    return errorActionState("An unexpected error occurred");
  }
});
