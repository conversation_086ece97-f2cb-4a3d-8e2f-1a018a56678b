"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Mail, Phone, MapPin, User } from "lucide-react";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  address?: string;
  relationship_type: string;
}

interface ContactDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  contact: Contact | null;
}

/**
 * Contact Details Drawer
 * Shows detailed contact information in a side drawer
 */
export function ContactDrawer({ isOpen, onClose, contact }: ContactDrawerProps) {
  if (!contact) return null;

  // Extract email from JSONB field
  const getEmail = () => {
    if (!contact.email) return null;
    if (typeof contact.email === "string") return contact.email;
    return contact.email.personal || contact.email.work || null;
  };

  // Extract phone from JSONB field
  const getPhone = () => {
    if (!contact.phone) return null;
    if (typeof contact.phone === "string") return contact.phone;
    return contact.phone.mobile || contact.phone.home || null;
  };

  // Extract multiple emails if available
  const getAllEmails = () => {
    if (!contact.email) return [];
    if (typeof contact.email === "string") return [{ type: "Email", value: contact.email }];

    const emails = [];
    if (contact.email.personal) emails.push({ type: "Personal", value: contact.email.personal });
    if (contact.email.work) emails.push({ type: "Work", value: contact.email.work });
    return emails;
  };

  // Extract multiple phones if available
  const getAllPhones = () => {
    if (!contact.phone) return [];
    if (typeof contact.phone === "string") return [{ type: "Phone", value: contact.phone }];

    const phones = [];
    if (contact.phone.mobile) phones.push({ type: "Mobile", value: contact.phone.mobile });
    if (contact.phone.home) phones.push({ type: "Home", value: contact.phone.home });
    if (contact.phone.work) phones.push({ type: "Work", value: contact.phone.work });
    return phones;
  };

  const allEmails = getAllEmails();
  const allPhones = getAllPhones();

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent>
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {contact.name}
          </SheetTitle>
          <SheetDescription>Contact information and details</SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-sm font-medium mb-3">Basic Information</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Name</span>
                <span className="text-sm font-medium">{contact.name}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Relationship</span>
                <Badge variant="secondary">{contact.relationship_type}</Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div>
            <h3 className="text-sm font-medium mb-3">Contact Information</h3>
            <div className="space-y-4">
              {/* Emails */}
              {allEmails.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Email</span>
                  </div>
                  <div className="space-y-1 ml-6">
                    {allEmails.map((email, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">{email.type}</span>
                        <a
                          href={`mailto:${email.value}`}
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {email.value}
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Phones */}
              {allPhones.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Phone</span>
                  </div>
                  <div className="space-y-1 ml-6">
                    {allPhones.map((phone, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">{phone.type}</span>
                        <a
                          href={`tel:${phone.value}`}
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {phone.value}
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Address */}
              {contact.address && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Address</span>
                  </div>
                  <div className="ml-6">
                    <p className="text-sm text-muted-foreground">{contact.address}</p>
                  </div>
                </div>
              )}

              {/* No contact info */}
              {allEmails.length === 0 && allPhones.length === 0 && !contact.address && (
                <p className="text-sm text-muted-foreground italic">
                  No contact information available
                </p>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
