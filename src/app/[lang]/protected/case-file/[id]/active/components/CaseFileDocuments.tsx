"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { DocumentList } from "@/components/ui/document-list";
import { FileText, Users } from "lucide-react";
import { motion } from "framer-motion";

interface CaseFileDocumentsProps {
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    caseDocuments: string;
    contactDocuments: string;
    noDocuments: string;
    noDocumentsDescription: string;
    uploadDocument: string;
    viewDocument: string;
    downloadDocument: string;
    deleteDocument: string;
    documentUploaded: string;
    documentDeleted: string;
  };
}

export function CaseFileDocuments({ caseFileId }: CaseFileDocumentsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="case" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="case" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Case Documents
              </TabsTrigger>
              <TabsTrigger value="contacts" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Contact Documents
              </TabsTrigger>
            </TabsList>

            <TabsContent value="case" className="space-y-4">
              <DocumentList
                entityType="case_file"
                entityId={caseFileId}
                title="Case Documents"
                description="Documents attached directly to this case file"
              />
            </TabsContent>

            <TabsContent value="contacts" className="space-y-4">
              <DocumentList
                entityType="case_file_contacts"
                entityId={caseFileId}
                title="Contact Documents"
                description="Documents attached to contacts in this case file"
                allowUpload={false}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}
