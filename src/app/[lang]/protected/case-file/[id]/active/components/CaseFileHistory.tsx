import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { HistoryTimeline } from "@/components/ui/history-timeline/history-timeline";
import { History } from "lucide-react";
import { fetchCaseFileHistory } from "../../../actions/history";

interface CaseFileHistoryProps {
  caseFileId: string;
  lang: string;
}

/**
 * Case File History component
 * Displays the complete history timeline for a case file
 */
export async function CaseFileHistory({ caseFileId }: CaseFileHistoryProps) {
  // Fetch case file history using the server action
  const historyItems = await fetchCaseFileHistory(caseFileId);

  return (
    <Card id="case-file-history">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Case File History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <HistoryTimeline
          items={historyItems}
          title="Case File History"
          showTitle={false}
          className="border-0 shadow-none"
        />
      </CardContent>
    </Card>
  );
}
