import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { HistoryTimeline } from "@/components/ui/history-timeline/history-timeline";
import { History } from "lucide-react";
import { fetchCaseFileHistory } from "../../../actions/history";

interface CaseFileHistoryProps {
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    noHistory: string;
    noHistoryDescription: string;
    changeDetails: string;
    viewDetails: string;
    hideDetails: string;
    changedBy: string;
    changedAt: string;
    actions: {
      INSERT: string;
      UPDATE: string;
      DELETE: string;
    };
  };
}

/**
 * Case File History component
 * Displays the complete history timeline for a case file
 */
export async function CaseFileHistory({ caseFileId, dictionary }: CaseFileHistoryProps) {
  // Default translations
  const defaultDictionary = {
    title: "Case File History",
    description: "Timeline of changes to this case file",
    noHistory: "No history found",
    noHistoryDescription: "Changes will appear here as they are made",
    changeDetails: "Change Details",
    viewDetails: "View Details",
    hideDetails: "Hide Details",
    changedBy: "Changed by",
    changedAt: "Changed at",
    actions: {
      INSERT: "Created",
      UPDATE: "Updated",
      DELETE: "Deleted",
    },
  };

  const t = dictionary || defaultDictionary;

  // Fetch case file history using the server action
  const historyItems = await fetchCaseFileHistory(caseFileId);

  return (
    <Card id="case-file-history">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          {t.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <HistoryTimeline
          items={historyItems}
          title={t.title}
          showTitle={false}
          className="border-0 shadow-none"
        />
      </CardContent>
    </Card>
  );
}
