"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Users, Mail, Phone, MapPin, Eye } from "lucide-react";
import { ContactDrawer } from "./ContactDrawer";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  address?: string;
  relationship_type: string;
}

interface CaseFileContactsProps {
  contacts: Contact[];
}

/**
 * Case File Contacts component
 * Displays contacts in a grid with click to view details
 */
export function CaseFileContacts({ contacts }: CaseFileContactsProps) {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Extract email from JSONB field
  const getEmail = (contact: Contact) => {
    if (!contact.email) return null;
    if (typeof contact.email === "string") return contact.email;
    return contact.email.personal || contact.email.work || null;
  };

  // Extract phone from JSONB field
  const getPhone = (contact: Contact) => {
    if (!contact.phone) return null;
    if (typeof contact.phone === "string") return contact.phone;
    return contact.phone.mobile || contact.phone.home || null;
  };

  // Handle contact click
  const handleContactClick = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDrawerOpen(true);
  };

  // Close drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
    setSelectedContact(null);
  };

  if (!contacts || contacts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Case File Contacts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            No contacts found for this case file.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Case File Contacts ({contacts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contacts.map((contact) => {
              const email = getEmail(contact);
              const phone = getPhone(contact);

              return (
                <Card
                  key={contact.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleContactClick(contact)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{contact.name}</h4>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {contact.relationship_type}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleContactClick(contact);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Contact Info */}
                      <div className="space-y-2 text-xs text-muted-foreground">
                        {email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-3 w-3" />
                            <span className="truncate">{email}</span>
                          </div>
                        )}
                        {phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-3 w-3" />
                            <span>{phone}</span>
                          </div>
                        )}
                        {contact.address && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{contact.address}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Contact Details Drawer */}
      <ContactDrawer
        isOpen={isDrawerOpen}
        onClose={closeDrawer}
        contact={selectedContact}
      />
    </>
  );
}
