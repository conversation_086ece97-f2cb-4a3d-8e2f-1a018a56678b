import { CaseFileDashboardData } from "../../lib/types";
import { FamilyInformation } from "./FamilyInformation";
import { ServiceRequirements } from "./ServiceRequirements";
import { QuickActions } from "./QuickActions";
import { RecentActivity } from "./RecentActivity";

interface CaseFileDashboardProps {
  data: CaseFileDashboardData;
  lang: string;
}

/**
 * Main dashboard component for active case files
 * Displays comprehensive case file information and management tools
 */
export function CaseFileDashboard({ data, lang }: CaseFileDashboardProps) {
  const { caseFile, familyInfo, serviceRequirements, recentActivity } = data;

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <QuickActions caseFileId={caseFile.id} lang={lang} />

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Family Information */}
        <FamilyInformation familyInfo={familyInfo} caseFileId={caseFile.id} lang={lang} />

        {/* Service Requirements */}
        <ServiceRequirements
          serviceRequirements={serviceRequirements}
          caseFileId={caseFile.id}
          lang={lang}
        />
      </div>

      {/* Recent Activity */}
      <RecentActivity recentActivity={recentActivity} caseFileId={caseFile.id} lang={lang} />
    </div>
  );
}
