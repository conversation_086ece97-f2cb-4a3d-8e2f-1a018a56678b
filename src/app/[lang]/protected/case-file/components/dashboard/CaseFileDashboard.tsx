import { CaseFileDashboardData } from "../../lib/types";
import { FamilyInformation } from "./FamilyInformation";
import { ServiceRequirements } from "./ServiceRequirements";
import { QuickActions } from "./QuickActions";

interface CaseFileDashboardProps {
  data: CaseFileDashboardData;
  lang: string;
  dictionary?: {
    title: string;
    quickActions: {
      title: string;
      schedule: string;
      addDocument: string;
      createNote: string;
      viewHistory: string;
    };
    familyInformation: {
      title: string;
      description: string;
    };
    serviceRequirements: {
      title: string;
      description: string;
    };
  };
}

/**
 * Main dashboard component for active case files
 * Displays comprehensive case file information and management tools
 */
export function CaseFileDashboard({ data, lang, dictionary }: CaseFileDashboardProps) {
  const { caseFile, familyInfo, serviceRequirements } = data;

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <QuickActions
        caseFileId={caseFile.id}
        lang={lang}
        dictionary={dictionary?.quickActions}
      />

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Family Information */}
        <FamilyInformation
          familyInfo={familyInfo}
          caseFileId={caseFile.id}
          lang={lang}
          dictionary={dictionary?.familyInformation}
        />

        {/* Service Requirements */}
        <ServiceRequirements
          serviceRequirements={serviceRequirements}
          caseFileId={caseFile.id}
          lang={lang}
          dictionary={dictionary?.serviceRequirements}
        />
      </div>
    </div>
  );
}
