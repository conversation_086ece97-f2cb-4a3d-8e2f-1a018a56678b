"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Calendar, FileText, Edit, Eye } from "lucide-react";
import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface QuickActionsProps {
  caseFileId: string;
  lang: string;
}

/**
 * Quick Actions component for case file dashboard
 * Provides shortcuts to common case file management tasks
 */
export function QuickActions({ caseFileId, lang: _lang }: QuickActionsProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Handle file upload for Add Document button
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setIsUploading(true);

      for (const file of Array.from(files)) {
        const formData = new FormData();
        formData.append("files", file);
        formData.append("attached_to_type", "case_file");
        formData.append("attached_to_id", caseFileId);
        formData.append("name", file.name);

        const response = await fetch("/api/documents/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} document(s)`);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // Refresh the page to update document lists
      router.refresh();
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents");
    } finally {
      setIsUploading(false);
    }
  };

  // Handle View History button click
  const handleViewHistory = () => {
    const historyElement = document.getElementById("case-file-history");
    if (historyElement) {
      historyElement.scrollIntoView({
        behavior: "smooth",
        block: "start"
      });
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Hidden file input for document upload */}
        <Input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileUpload}
          id="quick-action-document-upload"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Calendar className="h-6 w-6" />
            <span>Schedule Appointment</span>
          </Button>
          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            <FileText className="h-6 w-6" />
            <span>{isUploading ? "Uploading..." : "Add Document"}</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Edit className="h-6 w-6" />
            <span>Create Note</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Eye className="h-6 w-6" />
            <span>View History</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
