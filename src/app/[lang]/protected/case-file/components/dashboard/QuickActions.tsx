import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Calendar, FileText, Edit, Eye } from "lucide-react";

interface QuickActionsProps {
  caseFileId: string;
  lang: string;
}

/**
 * Quick Actions component for case file dashboard
 * Provides shortcuts to common case file management tasks
 */
export function QuickActions({ caseFileId: _caseFileId, lang: _lang }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Calendar className="h-6 w-6" />
            <span>Schedule Appointment</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <FileText className="h-6 w-6" />
            <span>Add Document</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Edit className="h-6 w-6" />
            <span>Create Note</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Eye className="h-6 w-6" />
            <span>View History</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
