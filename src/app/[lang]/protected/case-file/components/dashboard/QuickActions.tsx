"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Calendar, FileText, Edit, Eye } from "lucide-react";
import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface QuickActionsProps {
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    schedule: string;
    addDocument: string;
    createNote: string;
    viewHistory: string;
  };
}

/**
 * Quick Actions component for case file dashboard
 * Provides shortcuts to common case file management tasks
 */
export function QuickActions({ caseFileId, lang: _lang, dictionary }: QuickActionsProps) {
  // Default translations
  const defaultDictionary = {
    title: "Quick Actions",
    schedule: "Schedule Appointment",
    addDocument: "Add Document",
    createNote: "Create Note",
    viewHistory: "View History",
  };

  const t = dictionary || defaultDictionary;
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Handle file upload for Add Document button
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setIsUploading(true);

      for (const file of Array.from(files)) {
        const formData = new FormData();
        formData.append("files", file);
        formData.append("attached_to_type", "case_file");
        formData.append("attached_to_id", caseFileId);
        formData.append("name", file.name);

        const response = await fetch("/api/documents/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} document(s)`);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // Refresh the page to update document lists
      router.refresh();
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents");
    } finally {
      setIsUploading(false);
    }
  };

  // Handle View History button click
  const handleViewHistory = () => {
    const historyElement = document.getElementById("case-file-history");
    if (historyElement) {
      historyElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          {t.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Hidden file input for document upload */}
        <Input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileUpload}
          id="quick-action-document-upload"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Calendar className="h-6 w-6" />
            <span>{t.schedule}</span>
          </Button>
          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            <FileText className="h-6 w-6" />
            <span>{isUploading ? "Uploading..." : t.addDocument}</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
            <Edit className="h-6 w-6" />
            <span>{t.createNote}</span>
          </Button>
          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={handleViewHistory}
          >
            <Eye className="h-6 w-6" />
            <span>{t.viewHistory}</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
