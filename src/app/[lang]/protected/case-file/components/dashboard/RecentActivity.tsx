import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { P } from "@/components/typography";
import { Calendar, FileText, Edit, Clock } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface RecentActivityProps {
  recentActivity: CaseFileDashboardData["recentActivity"];
  caseFileId: string;
  lang: string;
}

/**
 * Recent Activity component for case file dashboard
 * Displays timeline of recent case file activities
 */
export function RecentActivity({
  recentActivity,
  caseFileId: _caseFileId,
  lang: _lang,
}: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "appointment":
        return <Calendar className="mr-2" />;
      case "document":
        return <FileText className="mr-2" />;
      case "note":
        return <Edit className="mr-2" />;
      case "status_change":
        return <Clock className="mr-2" />;
      default:
        return <FileText className="mr-2" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentActivity.length > 0 ? (
            recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  {getActivityIcon(activity.type)}
                  <span>{activity.description}</span>
                </div>
                <span>{formatTimestamp(activity.timestamp)}</span>
              </div>
            ))
          ) : (
            <div>
              <FileText />
              <P>No recent activity</P>
            </div>
          )}
        </div>
        <div>
          <Button variant="outline">View Full History</Button>
        </div>
      </CardContent>
    </Card>
  );
}
