import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { P } from "@/components/typography";
import { FileText, AlertCircle } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface ServiceRequirementsProps {
  serviceRequirements: CaseFileDashboardData["serviceRequirements"];
  caseFileId: string;
  lang: string;
}

/**
 * Service Requirements component for case file dashboard
 * Displays service progress and pending items
 */
export function ServiceRequirements({
  serviceRequirements,
  caseFileId: _caseFileId,
  lang: _lang,
}: ServiceRequirementsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Service Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <P>
              {serviceRequirements.completedServices}/{serviceRequirements.totalServices}
            </P>
            <P>Services Completed</P>
          </div>
          <div>
            <P>{serviceRequirements.upcomingAppointments}</P>
            <P>Upcoming Appointments</P>
          </div>
        </div>

        {/* Pending Items */}
        {serviceRequirements.pendingDocuments > 0 && (
          <div className="flex items-center">
            <AlertCircle className="mr-2" />
            <div>
              <P>{serviceRequirements.pendingDocuments} pending document(s)</P>
              <P>Requires attention</P>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Button variant="outline">View Service Timeline</Button>
          <Button variant="outline">View All Appointments</Button>
        </div>
      </CardContent>
    </Card>
  );
}
