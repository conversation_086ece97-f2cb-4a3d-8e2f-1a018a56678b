"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";
import { Muted } from "@/components/typography";

interface SearchAndFilterProps {
  lang: string;
}

/**
 * Search and Filter component for case files
 * Provides interactive search and filtering capabilities
 */
export function SearchAndFilter({ lang }: SearchAndFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");
  const [statusFilter, setStatusFilter] = useState(searchParams.get("status") || "all");

  const handleSearch = () => {
    const params = new URLSearchParams();

    if (searchQuery.trim()) {
      params.set("search", searchQuery.trim());
    }

    if (statusFilter && statusFilter !== "all") {
      params.set("status", statusFilter);
    }

    // Reset to first page when searching
    params.delete("page");

    const queryString = params.toString();
    const newUrl = queryString
      ? `/${lang}/protected/case-file/list?${queryString}`
      : `/${lang}/protected/case-file/list`;

    router.push(newUrl);
  };

  const handleClearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    router.push(`/${lang}/protected/case-file/list`);
  };

  const hasActiveFilters = searchQuery.trim() || (statusFilter && statusFilter !== "all");

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Search />
          Search & Filter
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by case number or contact name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="opening">Opening</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button onClick={handleSearch}>
                <Search />
                Search
              </Button>
              {hasActiveFilters && (
                <Button variant="outline" onClick={handleClearFilters}>
                  <X />
                  Clear Filters
                </Button>
              )}
            </div>

            {hasActiveFilters && (
              <Muted>
                Active filters:{" "}
                {[
                  searchQuery.trim() && `"${searchQuery.trim()}"`,
                  statusFilter && statusFilter !== "all" && `Status: ${statusFilter}`,
                ]
                  .filter(Boolean)
                  .join(", ")}
              </Muted>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
