"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { CaseFileStatus } from "../../lib/types";

interface CaseFileNavigationProps {
  caseFileId: string;
  currentStatus: CaseFileStatus;
  lang: string;
}

/**
 * Case File Navigation component
 * Provides navigation between different case file states
 */
export function CaseFileNavigation({ caseFileId, currentStatus, lang }: CaseFileNavigationProps) {
  const pathname = usePathname();

  const navigationItems = [
    {
      status: "opening" as CaseFileStatus,
      label: "Opening",
      path: `/${lang}/protected/case-file/${caseFileId}/opening`,
      color: "bg-orange-100 text-orange-800",
      available: true,
    },
    {
      status: "active" as CaseFileStatus,
      label: "Active",
      path: `/${lang}/protected/case-file/${caseFileId}/active`,
      color: "bg-green-100 text-green-800",
      available: currentStatus !== "opening",
    },
    {
      status: "suspended" as CaseFileStatus,
      label: "Suspended",
      path: `/${lang}/protected/case-file/${caseFileId}/suspended`,
      color: "bg-yellow-100 text-yellow-800",
      available: currentStatus === "suspended",
    },
    {
      status: "closed" as CaseFileStatus,
      label: "Closed",
      path: `/${lang}/protected/case-file/${caseFileId}/closed`,
      color: "bg-gray-100 text-gray-800",
      available: currentStatus === "closed",
    },
  ];

  return (
    <div className="flex flex-wrap gap-2">
      {navigationItems.map((item) => {
        const isActive = pathname === item.path;
        const isCurrent = item.status === currentStatus;
        if (!item.available && !isCurrent) {
          return (
            <Button key={item.status} variant="outline" size="sm" disabled className="opacity-50">
              {item.label}
            </Button>
          );
        }

        return (
          <Button
            key={item.status}
            variant={isActive ? "default" : "outline"}
            size="sm"
            asChild={!isActive}
            className={isActive ? "" : ""}
          >
            {isActive ? (
              <span className="flex items-center gap-2">
                {item.label}
                {isCurrent && <Badge className={`text-xs ${item.color}`}>Current</Badge>}
              </span>
            ) : (
              <Link href={item.path} className="flex items-center gap-2">
                {item.label}
                {isCurrent && <Badge className={`text-xs ${item.color}`}>Current</Badge>}
              </Link>
            )}
          </Button>
        );
      })}
    </div>
  );
}
