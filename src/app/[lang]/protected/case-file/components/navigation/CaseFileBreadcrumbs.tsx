import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { CaseFileStatus } from "../../lib/types";

interface CaseFileBreadcrumbsProps {
  caseFileId: string;
  caseNumber: string;
  currentStatus: CaseFileStatus;
  lang: string;
}

/**
 * Case File Breadcrumbs component
 * Provides breadcrumb navigation for case file pages
 */
export function CaseFileBreadcrumbs({
  caseFileId,
  caseNumber,
  currentStatus,
  lang,
}: CaseFileBreadcrumbsProps) {
  const getStatusLabel = (status: CaseFileStatus) => {
    switch (status) {
      case "opening":
        return "Opening";
      case "active":
        return "Active";
      case "suspended":
        return "Suspended";
      case "closed":
        return "Closed";
      default:
        return status;
    }
  };

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href={`/${lang}/protected/case-file`}>Case Files</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/${lang}/protected/case-file/${caseFileId}`}>
            {caseNumber}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>{getStatusLabel(currentStatus)}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
}
