"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";

interface StatusFilterProps {
  lang: string;
}

export function StatusFilter({ lang }: StatusFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentStatus = searchParams.get("status") || "all";
  const currentSearch = searchParams.get("search") || "";

  const handleStatusChange = (value: string) => {
    const params = new URLSearchParams();

    // Preserve search parameter
    if (currentSearch) {
      params.set("search", currentSearch);
    }

    // Add status parameter if not "all"
    if (value !== "all") {
      params.set("status", value);
    }

    // Navigate to new URL
    const queryString = params.toString();
    const newUrl = `/${lang}/protected/case-file/list${queryString ? `?${queryString}` : ""}`;
    router.push(newUrl);
  };

  return (
    <Select value={currentStatus} onValueChange={handleStatusChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Filter by status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Statuses</SelectItem>
        <SelectItem value="opening">Opening</SelectItem>
        <SelectItem value="active">Active</SelectItem>
        <SelectItem value="suspended">Suspended</SelectItem>
        <SelectItem value="closed">Closed</SelectItem>
      </SelectContent>
    </Select>
  );
}
