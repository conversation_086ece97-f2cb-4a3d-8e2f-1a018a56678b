import { Progress } from "@/components/ui/progress";
import { P } from "@/components/typography";

interface ProgressIndicatorProps {
  completed: number;
  total: number;
  label: string;
  showPercentage?: boolean;
}

/**
 * Progress Indicator component
 * Displays progress with a progress bar and optional percentage
 */
export function ProgressIndicator({
  completed,
  total,
  label,
  showPercentage = true,
}: ProgressIndicatorProps) {
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <P className="text-sm font-medium">{label}</P>
        <div className="flex items-center gap-2">
          <P className="text-sm text-muted-foreground">
            {completed}/{total}
          </P>
          {showPercentage && <P className="text-sm font-medium">{percentage}%</P>}
        </div>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  );
}
