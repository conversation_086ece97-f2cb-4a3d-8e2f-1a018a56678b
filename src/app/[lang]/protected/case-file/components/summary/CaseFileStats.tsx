import { Card, CardContent } from "@/components/ui/card";
import { P } from "@/components/typography";

interface CaseFileStatsProps {
  statistics: {
    opening: number;
    active: number;
    suspended: number;
    closed: number;
    total: number;
  } | null;
}

/**
 * Case File Statistics component
 * Displays overview statistics for case files
 */
export function CaseFileStats({ statistics }: CaseFileStatsProps) {
  const stats = statistics || {
    opening: 0,
    active: 0,
    suspended: 0,
    closed: 0,
    total: 0,
  };

  const statItems = [
    {
      label: "Opening",
      value: stats.opening,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      label: "Active",
      value: stats.active,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      label: "Suspended",
      value: stats.suspended,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
    {
      label: "Closed",
      value: stats.closed,
      color: "text-gray-600",
      bgColor: "bg-gray-100",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {statItems.map((item) => (
        <Card key={item.label}>
          <CardContent className="p-6">
            <div className="text-center">
              <div className={`text-2xl font-bold ${item.color}`}>{item.value}</div>
              <P className={`text-sm ${item.color}`}>{item.label}</P>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
