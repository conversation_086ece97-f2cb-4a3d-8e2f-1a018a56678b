import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { P } from "@/components/typography";
import { FileText } from "lucide-react";
import Link from "next/link";
import { CaseFileSummary } from "../../lib/types";

interface CaseFileCardProps {
  caseFile: CaseFileSummary;
  lang: string;
}

/**
 * Case File Card component for displaying case file summaries
 * Used in lists and dashboard views
 */
export function CaseFileCard({ caseFile, lang }: CaseFileCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "opening":
        return "bg-orange-100 text-orange-800";
      case "suspended":
        return "bg-yellow-100 text-yellow-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getIconColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-600";
      case "opening":
        return "bg-orange-100 text-orange-600";
      case "suspended":
        return "bg-yellow-100 text-yellow-600";
      case "closed":
        return "bg-gray-100 text-gray-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusDate = () => {
    switch (caseFile.status) {
      case "active":
        return caseFile.activated_at
          ? `Active since ${formatDate(caseFile.activated_at)}`
          : "Active";
      case "opening":
        return `Opening since ${formatDate(caseFile.created_at)}`;
      case "suspended":
        return caseFile.suspended_at
          ? `Suspended since ${formatDate(caseFile.suspended_at)}`
          : "Suspended";
      case "closed":
        return caseFile.closed_at ? `Closed on ${formatDate(caseFile.closed_at)}` : "Closed";
      default:
        return formatDate(caseFile.created_at);
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`p-2 rounded-full mr-3 ${getIconColor(caseFile.status)}`}>
              <FileText className="h-5 w-5" />
            </div>
            <div>
              <P className="font-medium">{caseFile.case_number}</P>
              {caseFile.primaryContactName && (
                <P className="text-sm text-muted-foreground">{caseFile.primaryContactName}</P>
              )}
              <P className="text-sm text-muted-foreground">{getStatusDate()}</P>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right text-sm text-muted-foreground">
              {caseFile.totalContacts > 0 && (
                <P className="text-xs">{caseFile.totalContacts} contacts</P>
              )}
              {caseFile.upcomingAppointments > 0 && (
                <P className="text-xs">{caseFile.upcomingAppointments} appointments</P>
              )}
              {caseFile.pendingDocuments > 0 && (
                <P className="text-xs text-yellow-600">{caseFile.pendingDocuments} pending docs</P>
              )}
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge className={`text-xs capitalize ${getStatusColor(caseFile.status)}`}>
                {caseFile.status}
              </Badge>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/${lang}/protected/case-file/${caseFile.id}`}>View</Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
