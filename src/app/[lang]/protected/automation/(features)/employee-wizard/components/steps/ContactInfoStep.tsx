"use server";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EmployeeDraftInsert } from "../../types/employee";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";

interface ContactInfoStepProps {
  draft: EmployeeDraftInsert;
}

/**
 * Contact information step for employee creation
 */
export default async function ContactInfoStep({ draft }: ContactInfoStepProps) {
  const data = draft.data;
  const dictionary = await getDomainFeatureDictionary("employee", "wizard");

  return (
    <div className="space-y-6">
      <div className="grid gap-3">
        <Label className="font-medium">{dictionary.fields?.email || "Email Addresses"}</Label>
        <div className="space-y-2">
          {/* Empty field for adding a new email */}
          <div className="flex gap-2">
            <Input
              defaultValue={data.email}
              required
              type="email"
              name={`email`}
              placeholder={dictionary.placeholders?.enterEmail || "Add email address"}
            />
          </div>
        </div>
      </div>

      <div className="grid gap-3">
        <Label htmlFor="role" className="font-medium">
          {dictionary.fields?.role || "User Role"}
        </Label>
        <Select name="role" defaultValue={data.role || "SocialWorker"}>
          <SelectTrigger>
            <SelectValue placeholder={dictionary.placeholders?.selectRole || "Select role"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Director">{dictionary.roles?.director || "Director"}</SelectItem>
            <SelectItem value="Coordinator">
              {dictionary.roles?.coordinator || "Coordinator"}
            </SelectItem>
            <SelectItem value="SocialWorker">
              {dictionary.roles?.socialWorker || "Social Worker"}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
