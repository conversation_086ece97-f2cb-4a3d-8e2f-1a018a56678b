import { logger } from "@/lib/logger/services/LoggerService";

export interface FileValidationResult {
  success: boolean;
  error?: string;
  warnings?: string[];
}

export interface FileValidationConfig {
  maxFileSize?: number; // in bytes
  allowedMimeTypes?: string[];
  allowedExtensions?: string[];
  maxFiles?: number;
  requireExtension?: boolean;
}

/**
 * Service for validating uploaded files
 * Provides comprehensive file validation including type, size, and security checks
 */
export class FileValidationService {
  /**
   * Default validation configuration
   */
  private static readonly DEFAULT_CONFIG: Required<FileValidationConfig> = {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: [
      // Documents
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
      "text/csv",
      // Images
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
      // Archives
      "application/zip",
      "application/x-rar-compressed",
    ],
    allowedExtensions: [
      // Documents
      "pdf",
      "doc",
      "docx",
      "xls",
      "xlsx",
      "ppt",
      "pptx",
      "txt",
      "csv",
      // Images
      "jpg",
      "jpeg",
      "png",
      "gif",
      "webp",
      "svg",
      // Archives
      "zip",
      "rar",
    ],
    maxFiles: 10,
    requireExtension: true,
  };

  /**
   * Validate a single file
   */
  static validateFile(file: File, config: FileValidationConfig = {}): FileValidationResult {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const warnings: string[] = [];

    try {
      // Check if file exists
      if (!file) {
        return { success: false, error: "No file provided" };
      }

      // Check file name
      if (!file.name || file.name.trim() === "") {
        return { success: false, error: "File name is required" };
      }

      // Check file size
      if (file.size === 0) {
        return { success: false, error: "File is empty" };
      }

      if (file.size > finalConfig.maxFileSize) {
        const maxSizeMB = Math.round(finalConfig.maxFileSize / (1024 * 1024));
        return {
          success: false,
          error: `File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${maxSizeMB}MB)`,
        };
      }

      // Extract file extension
      const extension = this.getFileExtension(file.name);

      if (finalConfig.requireExtension && !extension) {
        return { success: false, error: "File must have a valid extension" };
      }

      // Validate file extension
      if (extension && !finalConfig.allowedExtensions.includes(extension.toLowerCase())) {
        return {
          success: false,
          error: `File extension '.${extension}' is not allowed. Allowed extensions: ${finalConfig.allowedExtensions.join(", ")}`,
        };
      }

      // Validate MIME type
      if (file.type && !finalConfig.allowedMimeTypes.includes(file.type)) {
        return {
          success: false,
          error: `File type '${file.type}' is not allowed`,
        };
      }

      // Check for MIME type and extension mismatch
      if (file.type && extension) {
        const mismatchWarning = this.checkMimeExtensionMismatch(file.type, extension);
        if (mismatchWarning) {
          warnings.push(mismatchWarning);
        }
      }

      // Security checks
      const securityCheck = this.performSecurityChecks(file);
      if (!securityCheck.success) {
        return securityCheck;
      }
      if (securityCheck.warnings) {
        warnings.push(...securityCheck.warnings);
      }

      logger.info(`File validation passed: ${file.name} (${this.formatFileSize(file.size)})`);

      return {
        success: true,
        warnings: warnings.length > 0 ? warnings : undefined,
      };
    } catch (error) {
      logger.error("Error validating file:", error as Error);
      return { success: false, error: "File validation failed due to an internal error" };
    }
  }

  /**
   * Validate multiple files
   */
  static validateFiles(files: File[], config: FileValidationConfig = {}): FileValidationResult {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };

    try {
      // Check number of files
      if (files.length === 0) {
        return { success: false, error: "No files provided" };
      }

      if (files.length > finalConfig.maxFiles) {
        return {
          success: false,
          error: `Too many files. Maximum allowed: ${finalConfig.maxFiles}, provided: ${files.length}`,
        };
      }

      // Calculate total size
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const maxTotalSize = finalConfig.maxFileSize * files.length;

      if (totalSize > maxTotalSize) {
        return {
          success: false,
          error: `Total file size (${this.formatFileSize(totalSize)}) exceeds maximum allowed (${this.formatFileSize(maxTotalSize)})`,
        };
      }

      // Validate each file
      const allWarnings: string[] = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const result = this.validateFile(file, config);

        if (!result.success) {
          return {
            success: false,
            error: `File ${i + 1} (${file.name}): ${result.error}`,
          };
        }

        if (result.warnings) {
          allWarnings.push(...result.warnings.map((w) => `${file.name}: ${w}`));
        }
      }

      // Check for duplicate file names
      const fileNames = files.map((f) => f.name.toLowerCase());
      const duplicates = fileNames.filter((name, index) => fileNames.indexOf(name) !== index);
      if (duplicates.length > 0) {
        allWarnings.push(`Duplicate file names detected: ${[...new Set(duplicates)].join(", ")}`);
      }

      logger.info(
        `Batch file validation passed: ${files.length} files (${this.formatFileSize(totalSize)} total)`
      );

      return {
        success: true,
        warnings: allWarnings.length > 0 ? allWarnings : undefined,
      };
    } catch (error) {
      logger.error("Error validating files:", error as Error);
      return { success: false, error: "Batch file validation failed due to an internal error" };
    }
  }

  /**
   * Perform security checks on a file
   */
  private static performSecurityChecks(file: File): FileValidationResult {
    const warnings: string[] = [];

    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app)$/i,
      /^\./, // Hidden files
      /\.\./, // Path traversal
      /%[0-9a-f]{2}/i, // URL encoded characters
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(file.name)) {
        return { success: false, error: "File name contains suspicious patterns" };
      }
    }

    // Check file name length
    if (file.name.length > 255) {
      return { success: false, error: "File name is too long (maximum 255 characters)" };
    }

    // Check for null bytes
    if (file.name.includes("\0")) {
      return { success: false, error: "File name contains invalid characters" };
    }

    // Warn about very large files
    if (file.size > 10 * 1024 * 1024) {
      // 10MB
      warnings.push("Large file detected - upload may take longer");
    }

    return { success: true, warnings: warnings.length > 0 ? warnings : undefined };
  }

  /**
   * Check for MIME type and extension mismatch
   */
  private static checkMimeExtensionMismatch(mimeType: string, extension: string): string | null {
    const mimeExtensionMap: Record<string, string[]> = {
      "application/pdf": ["pdf"],
      "application/msword": ["doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ["docx"],
      "image/jpeg": ["jpg", "jpeg"],
      "image/png": ["png"],
      "image/gif": ["gif"],
      "text/plain": ["txt"],
      "text/csv": ["csv"],
    };

    const expectedExtensions = mimeExtensionMap[mimeType];
    if (expectedExtensions && !expectedExtensions.includes(extension.toLowerCase())) {
      return `MIME type '${mimeType}' doesn't match file extension '.${extension}'`;
    }

    return null;
  }

  /**
   * Get file extension from filename
   */
  private static getFileExtension(filename: string): string | null {
    const lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
      return null;
    }
    return filename.substring(lastDotIndex + 1);
  }

  /**
   * Format file size in human-readable format
   */
  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get validation configuration for specific entity types
   */
  static getConfigForEntityType(entityType: string): FileValidationConfig {
    const configs: Record<string, FileValidationConfig> = {
      case_file: {
        maxFileSize: 100 * 1024 * 1024, // 100MB for case files
        maxFiles: 20,
      },
      contact: {
        maxFileSize: 25 * 1024 * 1024, // 25MB for contacts
        maxFiles: 10,
      },
      appointment: {
        maxFileSize: 10 * 1024 * 1024, // 10MB for appointments
        maxFiles: 5,
      },
      request: {
        maxFileSize: 50 * 1024 * 1024, // 50MB for requests
        maxFiles: 15,
      },
    };

    return configs[entityType] || {};
  }

  /**
   * Validate file content (basic checks)
   */
  static async validateFileContent(file: File): Promise<FileValidationResult> {
    try {
      // Read first few bytes to check file signature
      const buffer = await file.slice(0, 16).arrayBuffer();
      const bytes = new Uint8Array(buffer);

      // Check for common file signatures
      const signatures: Record<string, number[]> = {
        pdf: [0x25, 0x50, 0x44, 0x46], // %PDF
        png: [0x89, 0x50, 0x4e, 0x47], // PNG
        jpeg: [0xff, 0xd8, 0xff], // JPEG
        gif: [0x47, 0x49, 0x46], // GIF
        zip: [0x50, 0x4b, 0x03, 0x04], // ZIP
      };

      // Basic signature validation
      for (const [type, signature] of Object.entries(signatures)) {
        if (signature.every((byte, index) => bytes[index] === byte)) {
          logger.verbose(`File signature validated: ${type}`);
          break;
        }
      }

      return { success: true };
    } catch (error) {
      logger.error("Error validating file content:", error as Error);
      return { success: false, error: "Failed to validate file content" };
    }
  }
}
