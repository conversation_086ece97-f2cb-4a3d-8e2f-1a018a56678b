import { redirect } from "next/navigation";
import DOMAIN_CONFIG from "../../lib/config/domain";

interface TemplatePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for the Template Single CRUD feature
 * Redirects to the management page
 */
export default async function TemplatePage({ params }: TemplatePageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the management page
  redirect(`/${resolvedParams.lang}${DOMAIN_CONFIG.basePath}/attachments/list`);
}
