"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUploader } from "@/components/ui/file-uploader";
// REMOVED: uploadAttachment import - Using API route instead
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface UploadFormProps {
  lang: string;
  entityType?: string;
  entityId?: string;
  returnUrl?: string;
}

export function UploadForm({ lang, entityType = "", entityId = "", returnUrl }: UploadFormProps) {
  const router = useRouter();
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    tags: "",
    attached_to_type: entityType,
    attached_to_id: entityId,
  });

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file");
      return;
    }

    if (!formData.name.trim()) {
      toast.error("Please provide a document name");
      return;
    }

    if (!formData.attached_to_type || !formData.attached_to_id) {
      toast.error("Please specify entity type and ID");
      return;
    }

    setIsUploading(true);

    try {
      const uploadFormData = new FormData();

      // Add files
      files.forEach((file) => {
        uploadFormData.append("files", file);
      });

      // Add metadata
      uploadFormData.append("name", formData.name);
      uploadFormData.append("description", formData.description);
      uploadFormData.append("category", formData.category);
      uploadFormData.append("tags", formData.tags);
      uploadFormData.append("attached_to_type", formData.attached_to_type);
      uploadFormData.append("attached_to_id", formData.attached_to_id);

      // Use API route instead of server action
      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: uploadFormData,
      });

      const result = await response.json();

      if (response.ok) {
        toast.success("Files uploaded successfully");

        // Redirect back or to attachments page
        const redirectUrl = returnUrl || `/${lang}/protected/document/attachments`;
        router.push(redirectUrl);
      } else {
        toast.error(result.error || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Upload failed due to an unexpected error");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Upload Area */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Select Files</CardTitle>
            <CardDescription>Drag and drop files here or click to browse</CardDescription>
          </CardHeader>
          <CardContent>
            <FileUploader
              onFilesSelected={handleFilesSelected}
              maxFiles={10}
              maxFileSize={50 * 1024 * 1024} // 50MB
              allowedTypes={[
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "image/jpeg",
                "image/png",
                "image/gif",
                "text/plain",
              ]}
              multiple={true}
              disabled={isUploading}
            />
          </CardContent>
        </Card>
      </div>

      {/* Sidebar - Document Details First */}
      <div className="space-y-4">
        {/* Document Details - Priority #1 */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Document Details</CardTitle>
            <CardDescription className="text-sm">Define document information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label htmlFor="name" className="text-sm font-medium">
                Name *
              </Label>
              <Input
                id="name"
                placeholder="Enter document name..."
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                placeholder="Optional description..."
                rows={2}
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="category" className="text-sm font-medium">
                Category
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange("category", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select category..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="legal">Legal Documents</SelectItem>
                  <SelectItem value="medical">Medical Records</SelectItem>
                  <SelectItem value="identification">Identification</SelectItem>
                  <SelectItem value="correspondence">Correspondence</SelectItem>
                  <SelectItem value="assessment">Assessment Reports</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tags" className="text-sm font-medium">
                Tags
              </Label>
              <Input
                id="tags"
                placeholder="Enter tags separated by commas..."
                value={formData.tags}
                onChange={(e) => handleInputChange("tags", e.target.value)}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Entity Selection */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Attach To</CardTitle>
            <CardDescription className="text-sm">
              Select the entity to attach documents to
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label htmlFor="entity-type" className="text-sm font-medium">
                Entity Type
              </Label>
              <Select
                value={formData.attached_to_type}
                onValueChange={(value) => handleInputChange("attached_to_type", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select entity type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="case_file">Case File</SelectItem>
                  <SelectItem value="case_file_contacts">Contact in Case File</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                  <SelectItem value="appointment">Appointment</SelectItem>
                  <SelectItem value="request">Request</SelectItem>
                  <SelectItem value="observation">Observation</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="entity-id" className="text-sm font-medium">
                Entity ID
              </Label>
              <Input
                id="entity-id"
                placeholder="Enter entity ID..."
                value={formData.attached_to_id}
                onChange={(e) => handleInputChange("attached_to_id", e.target.value)}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Upload Actions */}
        <Card>
          <CardContent className="pt-4">
            <div className="space-y-2">
              <Button
                className="w-full"
                onClick={handleUpload}
                disabled={isUploading || files.length === 0 || !formData.name.trim()}
              >
                {isUploading ? "Uploading..." : "Upload Documents"}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.back()}
                disabled={isUploading}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
