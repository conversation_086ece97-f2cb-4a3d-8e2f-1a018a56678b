"use client";

import { ReactNode } from "react";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface RelationshipModalProps {
  children: ReactNode;
  title?: string;
  description?: string;
  showCloseButton?: boolean;
}

/**
 * Modal component for relationship operations
 * Automatically closes when the user navigates back
 *
 * @example
 * ```tsx
 * <RelationshipModal
 *   title="Create Relationship"
 *   description="Add a new relationship to this contact"
 * >
 *   <CreateForm lang={lang} contactId={id} dictionary={dictionary} />
 * </RelationshipModal>
 * ```
 */
export function RelationshipModal({
  children,
  title,
  description,
  showCloseButton = true,
}: RelationshipModalProps) {
  const router = useRouter();

  return (
    <Dialog open={true} onOpenChange={() => router.back()}>
      <DialogContent className="sm:max-w-[600px]">
        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        )}

        {children}
      </DialogContent>
    </Dialog>
  );
}
