"use client";

import {
  <PERSON>er,
  <PERSON>er<PERSON>lose,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ContactItem } from "@contact/management/lib/types";

interface ContactDrawerProps {
  contact: ContactItem | null;
  isOpen: boolean;
  onClose: () => void;
  lang: string;
}

/**
 * Drawer component for displaying quick contact information
 */
export function ContactDrawer({ contact, isOpen, onClose, lang }: ContactDrawerProps) {
  if (!contact) return null;

  // Format email display
  const formatEmail = () => {
    const email = contact.email as Record<string, string>;
    if (!email) return "No email provided";

    const parts = [];
    if (email.personal) parts.push(`Personal: ${email.personal}`);
    if (email.work) parts.push(`Work: ${email.work}`);
    return parts.length > 0 ? parts.join(", ") : "No email provided";
  };

  // Format phone display
  const formatPhone = () => {
    const phone = contact.phone as Record<string, string>;
    if (!phone) return "No phone provided";

    const parts = [];
    if (phone.mobile) parts.push(`Mobile: ${phone.mobile}`);
    if (phone.work) parts.push(`Work: ${phone.work}`);
    if (phone.home) parts.push(`Home: ${phone.home}`);
    return parts.length > 0 ? parts.join(", ") : "No phone provided";
  };

  return (
    <Drawer open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DrawerContent>
        <div className="mx-auto w-full max-w-md">
          <DrawerHeader>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl font-semibold">
                {contact.name.charAt(0)}
              </div>
              <div>
                <DrawerTitle>{contact.name}</DrawerTitle>
                <DrawerDescription>Contact Information</DrawerDescription>
              </div>
            </div>
          </DrawerHeader>

          <div className="p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
              <p className="text-sm mt-1">{formatEmail()}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
              <p className="text-sm mt-1">{formatPhone()}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Address</h3>
              <p className="text-sm mt-1">{contact.address || "No address provided"}</p>
            </div>
          </div>

          <DrawerFooter className="flex flex-row justify-between">
            <DrawerClose asChild>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </DrawerClose>
            <Button asChild>
              <Link href={`/${lang}/protected/contact/management/${contact.id}/view`}>
                View Full Profile
              </Link>
            </Button>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
