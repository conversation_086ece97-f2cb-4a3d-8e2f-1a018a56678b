import { ReactNode } from "react";
import { view } from "../../../actions/view";
import { View } from "../../../components/View";
import { notFound } from "next/navigation";
import { ActionState } from "@/lib/types/responses";
import { ContactItem } from "../../../lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@contact/lib/config/domain";
import { FEATURE_ID } from "../../../lib/config/feature";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";

interface ViewLayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Layout for contact view with tabs for different sections
 * Uses the standardized SidebarLayout component for consistent UI
 */
export default async function ViewLayout({ children, params }: ViewLayoutProps) {
  const { lang, id } = await params;

  // Create initial state
  const initialState: ActionState<ContactItem> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch the contact using the server action
  const state = await view(initialState, { id });

  // If the contact is not found, show the not-found page
  if (!state.success || !state.data) {
    notFound();
  }

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);

  // Get the current path segment to determine active tab
  const pathname = new URL(`http://localhost/${lang}/protected/contact/management/${id}/view`)
    .pathname;
  const isRelationshipsTab = pathname.includes("/relationships");

  // Create the sidebar content with tabs
  const sidebar = (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">{"Additional Information"}</h2>
      <Tabs defaultValue={isRelationshipsTab ? "relationships" : "history"} className="w-full">
        <TabsList className="w-full">
          <TabsTrigger value="history" className="flex-1" asChild>
            <Link href={`/${lang}/protected/contact/management/${id}/view/history`}>
              {dictionary.history?.title || "History"}
            </Link>
          </TabsTrigger>
          <TabsTrigger value="relationships" className="flex-1" asChild>
            <Link href={`/${lang}/protected/contact/management/${id}/view/relationships`}>
              {dictionary.relationships?.title || "Relationships"}
            </Link>
          </TabsTrigger>
        </TabsList>
        <div className="mt-4">{children}</div>
      </Tabs>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main content - takes 2/3 of the space */}
        <div className="lg:col-span-2">
          <View lang={lang} item={state.data} dictionary={dictionary} />
        </div>

        {/* Sidebar - takes 1/3 of the space */}
        <div className="bg-muted/10 rounded-lg border">{sidebar}</div>
      </div>
    </div>
  );
}
