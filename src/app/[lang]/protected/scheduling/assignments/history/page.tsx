import { Suspense } from "react";
import { Locale } from "@/lib/i18n/settings";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { History, Search, Download, Calendar, User, Clock } from "lucide-react";
import { PageTitle } from "@/components/typography";
import { ListLoading } from "@/components/ui/list-loading";

interface AssignmentHistoryPageProps {
  params: Promise<{ lang: Locale }>;
}

export default async function AssignmentHistoryPage({ params }: AssignmentHistoryPageProps) {
  const { lang } = await params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <PageTitle>Assignment History</PageTitle>
          <p className="text-muted-foreground">Track and audit all employee assignment changes</p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export History
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search assignments..." className="pl-10" />
            </div>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Employee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                <SelectItem value="dr-johnson">Dr. Sarah Johnson</SelectItem>
                <SelectItem value="dr-chen">Dr. Michael Chen</SelectItem>
                <SelectItem value="dr-davis">Dr. Emily Davis</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Action Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="updated">Updated</SelectItem>
                <SelectItem value="removed">Removed</SelectItem>
                <SelectItem value="reassigned">Reassigned</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* History Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Assignment Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ListLoading />}>
            <div className="space-y-6">
              {[
                {
                  id: 1,
                  action: "Assignment Created",
                  employee: "Dr. Sarah Johnson",
                  appointment: "Family Consultation Session",
                  time: "2 hours ago",
                  user: "Admin User",
                  details: "Primary assignment for appointment #1234",
                  type: "created",
                },
                {
                  id: 2,
                  action: "Employee Reassigned",
                  employee: "Dr. Michael Chen → Dr. Emily Davis",
                  appointment: "Individual Assessment",
                  time: "4 hours ago",
                  user: "Scheduler",
                  details: "Original employee unavailable due to emergency",
                  type: "reassigned",
                },
                {
                  id: 3,
                  action: "Assignment Confirmed",
                  employee: "Dr. Sarah Johnson",
                  appointment: "Follow-up Session",
                  time: "6 hours ago",
                  user: "Dr. Johnson",
                  details: "Employee confirmed availability",
                  type: "updated",
                },
                {
                  id: 4,
                  action: "Secondary Employee Added",
                  employee: "Dr. Michael Chen",
                  appointment: "Group Therapy Session",
                  time: "1 day ago",
                  user: "Admin User",
                  details: "Added as secondary therapist for group session",
                  type: "created",
                },
                {
                  id: 5,
                  action: "Assignment Removed",
                  employee: "Dr. Emily Davis",
                  appointment: "Cancelled Appointment",
                  time: "2 days ago",
                  user: "Scheduler",
                  details: "Appointment cancelled by client",
                  type: "removed",
                },
              ].map((entry) => (
                <div key={entry.id} className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        entry.type === "created"
                          ? "bg-green-100 text-green-600"
                          : entry.type === "updated"
                            ? "bg-blue-100 text-blue-600"
                            : entry.type === "reassigned"
                              ? "bg-orange-100 text-orange-600"
                              : "bg-red-100 text-red-600"
                      }`}
                    >
                      {entry.type === "created" ? (
                        <User className="h-5 w-5" />
                      ) : entry.type === "updated" ? (
                        <Clock className="h-5 w-5" />
                      ) : entry.type === "reassigned" ? (
                        <History className="h-5 w-5" />
                      ) : (
                        <Calendar className="h-5 w-5" />
                      )}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{entry.action}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={
                            entry.type === "created"
                              ? "default"
                              : entry.type === "updated"
                                ? "secondary"
                                : entry.type === "reassigned"
                                  ? "outline"
                                  : "destructive"
                          }
                        >
                          {entry.type}
                        </Badge>
                        <span className="text-sm text-muted-foreground">{entry.time}</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      <strong>Employee:</strong> {entry.employee}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      <strong>Appointment:</strong> {entry.appointment}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      <strong>By:</strong> {entry.user}
                    </p>
                    <p className="text-sm mt-2">{entry.details}</p>
                  </div>
                </div>
              ))}
            </div>
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
