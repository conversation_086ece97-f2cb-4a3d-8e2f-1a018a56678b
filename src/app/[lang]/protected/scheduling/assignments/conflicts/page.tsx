import { Suspense } from "react";
import { Locale } from "@/lib/i18n/settings";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle, Clock, Users, Calendar } from "lucide-react";
import { PageTitle } from "@/components/typography";
import { ListLoading } from "@/components/ui/list-loading";

interface ConflictResolutionPageProps {
  params: Promise<{ lang: Locale }>;
}

export default async function ConflictResolutionPage({ params }: ConflictResolutionPageProps) {
  const { lang } = await params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <PageTitle>Assignment Conflicts</PageTitle>
          <p className="text-muted-foreground">Detect and resolve employee assignment conflicts</p>
        </div>
        <Button variant="outline">
          <Clock className="h-4 w-4 mr-2" />
          Refresh Conflicts
        </Button>
      </div>

      {/* Conflict Alert */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>3 Assignment Conflicts Detected</AlertTitle>
        <AlertDescription>
          Multiple employees are assigned to overlapping time slots. Immediate resolution required
          to prevent scheduling issues.
        </AlertDescription>
      </Alert>

      {/* Conflict Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Double Bookings</CardTitle>
            <Users className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">2</div>
            <p className="text-xs text-muted-foreground">Same employee, multiple appointments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Availability Conflicts</CardTitle>
            <Calendar className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">1</div>
            <p className="text-xs text-muted-foreground">Outside employee availability</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved Today</CardTitle>
            <Clock className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">5</div>
            <p className="text-xs text-muted-foreground">Successfully resolved</p>
          </CardContent>
        </Card>
      </div>

      {/* Conflict List */}
      <Card>
        <CardHeader>
          <CardTitle>Active Conflicts</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ListLoading />}>
            <div className="space-y-4">
              {/* Placeholder conflict items */}
              {[
                {
                  id: 1,
                  type: "Double Booking",
                  employee: "Dr. Sarah Johnson",
                  time: "Today 2:00 PM - 3:00 PM",
                  appointments: ["Family Consultation", "Individual Session"],
                  severity: "high",
                },
                {
                  id: 2,
                  type: "Double Booking",
                  employee: "Dr. Michael Chen",
                  time: "Tomorrow 10:00 AM - 11:00 AM",
                  appointments: ["Assessment", "Follow-up"],
                  severity: "high",
                },
                {
                  id: 3,
                  type: "Availability Conflict",
                  employee: "Dr. Emily Davis",
                  time: "Friday 6:00 PM - 7:00 PM",
                  appointments: ["Emergency Session"],
                  severity: "medium",
                },
              ].map((conflict) => (
                <div
                  key={conflict.id}
                  className="flex items-center justify-between p-4 border rounded-lg bg-red-50 border-red-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-red-900">{conflict.type}</h4>
                      <p className="text-sm text-red-700">
                        {conflict.employee} • {conflict.time}
                      </p>
                      <p className="text-xs text-red-600">
                        Conflicts: {conflict.appointments.join(", ")}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={conflict.severity === "high" ? "destructive" : "secondary"}>
                      {conflict.severity === "high" ? "High" : "Medium"}
                    </Badge>
                    <Button size="sm" variant="outline">
                      Resolve
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
