import { Suspense } from "react";
import { Locale } from "@/lib/i18n/settings";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { User, Calendar, Clock, MapPin, Edit, Trash2, History } from "lucide-react";
import { PageTitle } from "@/components/typography";
import { ListLoading } from "@/components/ui/list-loading";

interface AssignmentDetailsPageProps {
  params: Promise<{ lang: Locale; id: string }>;
}

export default async function AssignmentDetailsPage({ params }: AssignmentDetailsPageProps) {
  const { lang, id } = await params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <PageTitle>Assignment Details</PageTitle>
          <p className="text-muted-foreground">
            Assignment ID: {id} • View and manage assignment details
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <History className="h-4 w-4 mr-2" />
            View History
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Remove
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Assignment Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assignment Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Employee</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>Dr. Sarah Johnson</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Role</label>
                  <div className="mt-1">
                    <Badge variant="secondary">Primary</Badge>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Date & Time</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Today, May 24, 2025</span>
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>2:00 PM - 3:00 PM</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>Room 101, Main Building</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Assignment Status
                </label>
                <div className="mt-1">
                  <Badge variant="default">Confirmed</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appointment Details */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Title</label>
                <p className="mt-1 font-medium">Family Consultation Session</p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Description</label>
                <p className="mt-1 text-sm">
                  Initial consultation session with the Johnson family to assess current situation
                  and develop intervention plan.
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Service Type</label>
                <p className="mt-1">Family Therapy</p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Duration</label>
                <p className="mt-1">60 minutes</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" variant="outline">
                Check Conflicts
              </Button>
              <Button className="w-full" variant="outline">
                View Employee Schedule
              </Button>
              <Button className="w-full" variant="outline">
                Reassign Employee
              </Button>
              <Button className="w-full" variant="outline">
                Add Secondary Employee
              </Button>
            </CardContent>
          </Card>

          {/* Assignment History */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Changes</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<ListLoading />}>
                <div className="space-y-3">
                  {[
                    {
                      action: "Assignment Created",
                      time: "2 hours ago",
                      user: "Admin",
                    },
                    {
                      action: "Employee Confirmed",
                      time: "1 hour ago",
                      user: "Dr. Johnson",
                    },
                    {
                      action: "Room Assigned",
                      time: "30 minutes ago",
                      user: "Scheduler",
                    },
                  ].map((change, index) => (
                    <div key={index} className="text-sm">
                      <p className="font-medium">{change.action}</p>
                      <p className="text-muted-foreground">
                        {change.time} by {change.user}
                      </p>
                    </div>
                  ))}
                </div>
              </Suspense>
            </CardContent>
          </Card>

          {/* Related Assignments */}
          <Card>
            <CardHeader>
              <CardTitle>Related Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm">
                  <p className="font-medium">Dr. Johnson's Schedule</p>
                  <p className="text-muted-foreground">3 more appointments today</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">Room 101 Usage</p>
                  <p className="text-muted-foreground">Available after 3:00 PM</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
