import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AssignmentDetailsNotFound() {
  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle>Assignment Not Found</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            The assignment you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild className="w-full">
            <Link href="/protected/scheduling/assignments">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Assignments
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
