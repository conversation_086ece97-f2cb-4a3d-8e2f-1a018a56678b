"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { AvailabilityService } from "../lib/services";
import {
  EmployeeAvailabilityExceptionInsert,
  EmployeeAvailabilityExceptionUpdate,
} from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { ServiceResponse, errorResponse, successResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Get all availability exceptions for an employee
 * @param employeeId The employee ID to get availability exceptions for
 * @returns Action response with the availability exceptions
 */
export async function getAvailabilityExceptionsByEmployee(
  employeeId: string
): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getAvailabilityExceptionsByEmployee(employeeId);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getAvailabilityExceptionsByEmployee action: ${error}`);
    return errorResponse(error, "Failed to get availability exceptions");
  }
}

/**
 * Get a specific availability exception by ID
 * @param id The availability exception ID
 * @returns Action response with the availability exception
 */
export async function getAvailabilityExceptionById(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getAvailabilityExceptionById(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getAvailabilityExceptionById action: ${error}`);
    return errorResponse(error, "Failed to get availability exception");
  }
}

/**
 * Create a new availability exception
 * @param formData The form data containing the availability exception details
 * @returns Action response with the created availability exception
 */
export async function createAvailabilityException(formData: FormData): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return errorResponse(null, "Organization not found");
    }
    const organizationId = organization.id;

    const employeeId = formData.get("employee_id") as string;
    const exceptionDate = formData.get("exception_date") as string;
    const startTime = formData.get("start_time") as string;
    const endTime = formData.get("end_time") as string;
    const isAvailable = formData.get("is_available") === "true";
    const reason = (formData.get("reason") as string) || undefined;

    const exception: EmployeeAvailabilityExceptionInsert = {
      employee_id: employeeId,
      organization_id: organizationId,
      exception_date: exceptionDate,
      start_time: startTime,
      end_time: endTime,
      is_available: isAvailable,
      reason,
      created_by: currentUser.id,
    };

    const result = await AvailabilityService.createAvailabilityException(exception);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in createAvailabilityException action: ${error}`);
    return errorResponse(error, "Failed to create availability exception");
  }
}

/**
 * Update an existing availability exception
 * @param id The availability exception ID
 * @param formData The form data containing the updated availability exception details
 * @returns Action response with the updated availability exception
 */
export async function updateAvailabilityException(
  id: string,
  formData: FormData
): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const exceptionDate = formData.get("exception_date") as string;
    const startTime = formData.get("start_time") as string;
    const endTime = formData.get("end_time") as string;
    const isAvailable = formData.get("is_available") === "true";
    const reason = (formData.get("reason") as string) || undefined;

    const exception: EmployeeAvailabilityExceptionUpdate = {
      exception_date: exceptionDate,
      start_time: startTime,
      end_time: endTime,
      is_available: isAvailable,
      reason,
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateAvailabilityException(id, exception);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in updateAvailabilityException action: ${error}`);
    return errorResponse(error, "Failed to update availability exception");
  }
}

/**
 * Delete an availability exception
 * @param id The availability exception ID
 * @returns Action response with success status
 */
export async function deleteAvailabilityException(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.deleteAvailabilityException(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(null);
  } catch (error) {
    logger.error(`Error in deleteAvailabilityException action: ${error}`);
    return errorResponse(error, "Failed to delete availability exception");
  }
}
