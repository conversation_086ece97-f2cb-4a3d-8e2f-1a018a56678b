"use server";

import { revalidatePath } from "next/cache";
import { AvailabilityService } from "../lib/services";
import { EmployeeAvailabilityInsert, EmployeeAvailabilityUpdate } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { ServiceResponse, errorResponse, successResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Get all availability records for an employee
 * @param employeeId The employee ID to get availability for
 * @returns Action response with the availability records
 */
export async function getAvailabilityByEmployee(employeeId: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getAvailabilityByEmployee(employeeId);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getAvailabilityByEmployee action: ${error}`);
    return errorResponse(error, "Failed to get availability records");
  }
}

/**
 * Get a specific availability record by ID
 * @param id The availability record ID
 * @returns Action response with the availability record
 */
export async function getAvailabilityById(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getAvailabilityById(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getAvailabilityById action: ${error}`);
    return errorResponse(error, "Failed to get availability record");
  }
}

/**
 * Create a new availability record
 * @param formData The form data containing the availability record details
 * @returns Action response with the created availability record
 */
export async function createAvailability(formData: FormData): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return errorResponse(null, "Organization not found");
    }
    const organizationId = organization.id;

    const employeeId = formData.get("employee_id") as string;
    const dayOfWeek = parseInt(formData.get("day_of_week") as string);
    const startTime = formData.get("start_time") as string;
    const endTime = formData.get("end_time") as string;
    const isRecurring = formData.get("is_recurring") === "true";
    const recurrencePattern = (formData.get("recurrence_pattern") as string) || undefined;

    const availability: EmployeeAvailabilityInsert = {
      employee_id: employeeId,
      organization_id: organizationId,
      day_of_week: dayOfWeek,
      start_time: startTime,
      end_time: endTime,
      is_recurring: isRecurring,
      recurrence_pattern: recurrencePattern,
      created_by: currentUser.id,
    };

    const result = await AvailabilityService.createAvailability(availability);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in createAvailability action: ${error}`);
    return errorResponse(error, "Failed to create availability record");
  }
}

/**
 * Update an existing availability record
 * @param id The availability record ID
 * @param formData The form data containing the updated availability record details
 * @returns Action response with the updated availability record
 */
export async function updateAvailability(id: string, formData: FormData): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const dayOfWeek = parseInt(formData.get("day_of_week") as string);
    const startTime = formData.get("start_time") as string;
    const endTime = formData.get("end_time") as string;
    const isRecurring = formData.get("is_recurring") === "true";
    const recurrencePattern = (formData.get("recurrence_pattern") as string) || undefined;

    const availability: EmployeeAvailabilityUpdate = {
      day_of_week: dayOfWeek,
      start_time: startTime,
      end_time: endTime,
      is_recurring: isRecurring,
      recurrence_pattern: recurrencePattern,
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateAvailability(id, availability);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in updateAvailability action: ${error}`);
    return errorResponse(error, "Failed to update availability record");
  }
}

/**
 * Delete an availability record
 * @param id The availability record ID
 * @returns Action response with success status
 */
export async function deleteAvailability(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.deleteAvailability(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(null);
  } catch (error) {
    logger.error(`Error in deleteAvailability action: ${error}`);
    return errorResponse(error, "Failed to delete availability record");
  }
}
