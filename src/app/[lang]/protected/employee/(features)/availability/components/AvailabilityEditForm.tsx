"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AvailabilityGrid, TimeSlot } from "@/components/ui/scheduling/availability-grid";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { createAvailability, deleteAvailability } from "../actions/availability";

export interface AvailabilityItem {
  id: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
  is_recurring: boolean;
  recurrence_pattern?: string;
}

interface AvailabilityEditFormProps {
  employeeId: string;
  availabilityId?: string;
  initialData?: AvailabilityItem[];
  lang: string;
  dictionary: any;
}

export function AvailabilityEditForm({
  employeeId,
  availabilityId,
  initialData = [],
  lang,
  dictionary,
}: AvailabilityEditFormProps) {
  const router = useRouter();
  const featureDictionary = dictionary.employee.availability;
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Convert availability data to time slots
  const convertToTimeSlots = (availabilityItems: AvailabilityItem[]): TimeSlot[] => {
    const timeSlots: TimeSlot[] = [];

    availabilityItems.forEach((item) => {
      // Parse start and end times
      const startHour = parseInt(item.start_time.split(":")[0]);
      const endHour = parseInt(item.end_time.split(":")[0]);

      // Create a time slot for each hour in the range
      for (let hour = startHour; hour < endHour; hour++) {
        timeSlots.push({
          day: item.day_of_week,
          hour,
          available: true,
        });
      }
    });

    return timeSlots;
  };

  // Initialize time slots from initial data
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>(convertToTimeSlots(initialData));

  // Handle time slot toggle
  const handleTimeSlotToggle = (timeSlot: TimeSlot) => {
    setTimeSlots((prev) => {
      const index = prev.findIndex(
        (slot) => slot.day === timeSlot.day && slot.hour === timeSlot.hour
      );

      if (index !== -1) {
        const newSlots = [...prev];
        newSlots[index] = timeSlot;
        return newSlots;
      } else {
        return [...prev, timeSlot];
      }
    });
  };

  // Convert time slots to availability data
  const convertToAvailabilityData = (slots: TimeSlot[]) => {
    // Group slots by day
    const slotsByDay: Record<number, number[]> = {};

    slots
      .filter((slot) => slot.available)
      .forEach((slot) => {
        if (!slotsByDay[slot.day]) {
          slotsByDay[slot.day] = [];
        }
        slotsByDay[slot.day].push(slot.hour);
      });

    // Convert grouped slots to availability items
    const availabilityItems: { day_of_week: number; start_time: string; end_time: string }[] = [];

    Object.entries(slotsByDay).forEach(([day, hours]) => {
      // Sort hours
      hours.sort((a, b) => a - b);

      // Find consecutive ranges
      let startHour = hours[0];
      let currentHour = hours[0];

      for (let i = 1; i <= hours.length; i++) {
        if (i === hours.length || hours[i] !== currentHour + 1) {
          // End of a consecutive range
          availabilityItems.push({
            day_of_week: parseInt(day),
            start_time: `${startHour.toString().padStart(2, "0")}:00`,
            end_time: `${(currentHour + 1).toString().padStart(2, "0")}:00`,
          });

          if (i < hours.length) {
            startHour = hours[i];
            currentHour = hours[i];
          }
        } else {
          currentHour = hours[i];
        }
      }
    });

    return availabilityItems;
  };

  // Handle save
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Convert time slots to availability data
      const availabilityItems = convertToAvailabilityData(timeSlots);

      // If there's existing data, delete it first
      if (initialData.length > 0) {
        for (const item of initialData) {
          await deleteAvailability(item.id);
        }
      }

      // Create new availability items
      for (const item of availabilityItems) {
        const formData = new FormData();
        formData.append("employee_id", employeeId);
        formData.append("day_of_week", item.day_of_week.toString());
        formData.append("start_time", item.start_time);
        formData.append("end_time", item.end_time);
        formData.append("is_recurring", "true");

        await createAvailability(formData);
      }

      toast.success(featureDictionary.updateSuccess);
      router.push(`/${lang}/protected/employee/availability/${employeeId}/view`);
      router.refresh();
    } catch (error) {
      console.error("Error saving availability:", error);
      toast.error(featureDictionary.errorSubmitting);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setTimeSlots(convertToTimeSlots(initialData));
  };

  // Get day names from dictionary
  const getDayNames = () => {
    return [
      featureDictionary.daysOfWeek.sunday,
      featureDictionary.daysOfWeek.monday,
      featureDictionary.daysOfWeek.tuesday,
      featureDictionary.daysOfWeek.wednesday,
      featureDictionary.daysOfWeek.thursday,
      featureDictionary.daysOfWeek.friday,
      featureDictionary.daysOfWeek.saturday,
    ];
  };

  return (
    <div className="space-y-6">
      <AvailabilityGrid
        timeSlots={timeSlots}
        onTimeSlotToggle={handleTimeSlotToggle}
        startHour={9}
        endHour={17}
        daysOfWeek={getDayNames()}
        title={featureDictionary.regularHoursTitle}
        description={featureDictionary.regularHoursDescription}
        onSave={handleSave}
        onReset={handleReset}
        showFooter={false}
      />

      <div className="flex justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push(`/${lang}/protected/employee/availability/${employeeId}/view`)}
          disabled={isSubmitting}
        >
          {featureDictionary.cancelButton}
        </Button>
        <Button onClick={handleSave} disabled={isSubmitting}>
          {isSubmitting ? featureDictionary.submitting : featureDictionary.saveButton}
        </Button>
      </div>
    </div>
  );
}
