"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

import { FeatureDictionary } from "../../lib/types";

interface CreateButtonProps {
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Button component for creating a new Feature 1 item
 */
export function CreateButton({ lang, dictionary }: CreateButtonProps) {
  return (
    <Button asChild>
      <Link href={`/${lang}/protected/automation/employee-wizard`}>
        {dictionary.actions?.create || "Create Employee"}
      </Link>
    </Button>
  );
}
