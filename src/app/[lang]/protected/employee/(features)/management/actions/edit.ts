"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Update an existing employee
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const edit = requirePermission(MANAGEMENT_PERMISSIONS.EDIT)(async (
  _prevState: ActionState<Employee>,
  formData: FormData
): Promise<ActionState<Employee>> => {
  try {
    const id = _prevState.data?.id;
    if (!id) return errorActionState("Employee ID is required");

    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const employmentStatus = formData.get("employmentStatus") as
      | "active"
      | "inactive"
      | "terminated";
    const profile = await auth.getCurrentUserProfile();
    const lang = profile?.language;

    // Build update object dynamically
    const updateData: Record<string, any> = {
      first_name: firstName,
      last_name: lastName,
      employment_status: employmentStatus,
      updated_at: new Date().toISOString(),
    };

    const optionalFields = {
      job_title: formData.get("jobTitle"),
      department: formData.get("department"),
      profile_image: formData.get("profileImage"),
      date_of_birth: formData.get("dateOfBirth"),
      gender: formData.get("gender"),
      address: formData.get("address"),
      employee_id: formData.get("employeeId"),
      hire_date: formData.get("hireDate"),
      termination_date: formData.get("terminationDate"),
      supervisor_id: formData.get("supervisorId"),
      user_account_id: formData.get("userAccountId"),
    };

    // Add optional fields if they are not empty
    for (const [key, value] of Object.entries(optionalFields)) {
      if (value && value !== "unspecified") {
        updateData[key] = value;
      } else if (value === "unspecified") {
        updateData[key] = null;
      }
    }

    // Process specializations (string to array)
    const specializations = formData.get("specializations") as string;
    if (specializations) {
      updateData.specializations = specializations
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean);
    }

    // Parse JSON fields safely
    try {
      const jsonFields = ["certifications", "education", "emails", "phones"];
      for (const field of jsonFields) {
        const value = formData.get(field) as string;
        if (value) {
          updateData[field] = JSON.parse(value);
        }
      }
    } catch (error) {
      logger.error(`Error parsing JSON fields: ${error}`);
      return errorActionState(`Error parsing JSON fields. Please ensure they are valid JSON.`);
    }

    // Final update
    const result = await ManagementService.edit(id, updateData);

    return {
      success: true,
      data: result.data,
      error: "",
    };
  } catch (err) {
    logger.error(String(err));
    return errorActionState("An unexpected error occurred while updating the employee.");
  }
});
