"use client";

import { useActionState } from "react";
import { createBusinessHours } from "../../actions/createBusinessHours";
import { updateBusinessHours } from "../../actions/updateBusinessHours";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { BusinessHours, DAY_NAMES, DAY_NAMES_FR, Location } from "../../lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
interface BusinessHoursFormProps {
  lang: string;

  dictionary: any;
  businessHours?: BusinessHours;
  locations: Location[];
  isEdit?: boolean;
}

export function BusinessHoursForm({
  lang,
  dictionary,
  businessHours,
  locations,
  isEdit = false,
}: BusinessHoursFormProps) {
  const [state, formAction, pending] = useActionState(
    isEdit ? updateBusinessHours : createBusinessHours,
    {
      success: false,
      error: "",
    }
  );

  const [isClosed, setIsClosed] = useState(businessHours?.is_closed || false);

  // Get day names based on language
  const getDayNames = () => {
    return lang === "fr" ? DAY_NAMES_FR : DAY_NAMES;
  };

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {isEdit
              ? dictionary.organization?.businessHoursUpdated
              : dictionary.organization?.businessHoursCreated}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />
        {isEdit && <input type="hidden" name="id" value={businessHours?.id} />}

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="dayOfWeek">
                {dictionary.organization?.dayOfWeek} <span className="text-red-500">*</span>
              </Label>
              <Select
                name="dayOfWeek"
                defaultValue={businessHours?.day_of_week?.toString()}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder={dictionary.organization?.dayOfWeek} />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(getDayNames()).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">{dictionary.organization?.location}</Label>
              <Select name="locationId" defaultValue={businessHours?.location_id || "all"}>
                <SelectTrigger>
                  <SelectValue placeholder={dictionary.organization?.allLocations} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{dictionary.organization?.allLocations}</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isClosed"
              name="isClosed"
              checked={isClosed}
              onCheckedChange={(checked) => setIsClosed(checked as boolean)}
              defaultChecked={businessHours?.is_closed || false}
            />
            <Label htmlFor="isClosed">{dictionary.organization?.isClosed}</Label>
          </div>

          {!isClosed && (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="startTime">
                  {dictionary.organization?.startTime} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="startTime"
                  name="startTime"
                  type="time"
                  defaultValue={businessHours?.start_time || "09:00"}
                  required={!isClosed}
                  disabled={isClosed}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">
                  {dictionary.organization?.endTime} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="endTime"
                  name="endTime"
                  type="time"
                  defaultValue={businessHours?.end_time || "17:00"}
                  required={!isClosed}
                  disabled={isClosed}
                />
              </div>
            </div>
          )}
        </div>

        <Button type="submit" disabled={pending} className="w-full">
          {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
        </Button>
      </form>
    </div>
  );
}
