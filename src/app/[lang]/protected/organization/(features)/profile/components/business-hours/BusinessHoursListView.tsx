"use client";

import { useState } from "react";
import { BusinessHours, DAY_NAMES, DAY_NAMES_FR, Location } from "../../lib/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteBusinessHours } from "../../actions/deleteBusinessHours";

interface BusinessHoursListViewProps {
  businessHours: BusinessHours[];
  locations: Location[];
  lang: string;

  dictionary: any;
}

export function BusinessHoursListView({
  businessHours,
  locations,
  lang,
  dictionary,
}: BusinessHoursListViewProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [businessHoursToDelete, setBusinessHoursToDelete] = useState<string | null>(null);

  // Format time to display in 12-hour format
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "PM" : "AM";
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Get day name based on language
  const getDayName = (dayOfWeek: number) => {
    return lang === "fr"
      ? DAY_NAMES_FR[dayOfWeek as keyof typeof DAY_NAMES_FR]
      : DAY_NAMES[dayOfWeek as keyof typeof DAY_NAMES];
  };

  // Get location name by ID
  const getLocationName = (locationId: string | null) => {
    if (!locationId) return dictionary.organization?.allLocations || "All Locations";

    const location = locations.find((loc) => loc.id === locationId);
    return location ? location.name : dictionary.organization?.allLocations || "All Locations";
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    setBusinessHoursToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (businessHoursToDelete) {
      await deleteBusinessHours(businessHoursToDelete, lang);
      setIsDeleteDialogOpen(false);
      setBusinessHoursToDelete(null);
    }
  };

  if (businessHours.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            {dictionary.organization?.noBusinessHours ||
              "No business hours found. Add your first business hours."}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{dictionary.organization?.dayOfWeek}</TableHead>
            <TableHead>{dictionary.organization?.startTime}</TableHead>
            <TableHead>{dictionary.organization?.endTime}</TableHead>
            <TableHead>{dictionary.organization?.location}</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {businessHours.map((hours) => (
            <TableRow key={hours.id}>
              <TableCell className="font-medium">{getDayName(hours.day_of_week)}</TableCell>
              <TableCell>
                {hours.is_closed ? (
                  <Badge variant="destructive">{dictionary.organization?.isClosed}</Badge>
                ) : (
                  formatTime(hours.start_time)
                )}
              </TableCell>
              <TableCell>{hours.is_closed ? "-" : formatTime(hours.end_time)}</TableCell>
              <TableCell>{getLocationName(hours.location_id)}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Link
                    href={`/${lang}/protected/organization/profile/business-hours/edit/${hours.id}`}
                  >
                    <Button variant="outline" size="icon">
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button variant="outline" size="icon" onClick={() => handleDeleteClick(hours.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {dictionary.organization?.deleteBusinessHours || "Delete Business Hours"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {dictionary.organization?.deleteBusinessHoursConfirm ||
                "Are you sure you want to delete these business hours?"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{dictionary.organization?.cancel || "Cancel"}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground"
            >
              {dictionary.organization?.delete || "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
