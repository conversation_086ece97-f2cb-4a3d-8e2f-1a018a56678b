import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Home } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

/**
 * Loading state for the Request view page
 */
export default function ViewLoading() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Home className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Skeleton className="h-4 w-16" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Skeleton className="h-4 w-12" />
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back button */}
      <div>
        <Button variant="outline" size="sm" disabled className="mb-4">
          <ChevronLeft className="mr-2 h-4 w-4" />
          <Skeleton className="h-4 w-16" />
        </Button>
      </div>

      {/* Request details */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-40" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-64" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Header with title and status */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-40" />
            </div>
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-9 w-20" />
            </div>
          </div>

          <Separator />

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Essential details about this request</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Service Type</div>
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Priority</div>
                <Skeleton className="h-5 w-24" />
              </div>
              <div className="space-y-1 col-span-1 md:col-span-2">
                <div className="text-sm font-medium text-muted-foreground">Description</div>
                <Skeleton className="h-20 w-full" />
              </div>
            </CardContent>
          </Card>

          {/* Service Requirements */}
          <Card>
            <CardHeader>
              <CardTitle>Service Requirements</CardTitle>
              <CardDescription>Specific requirements for this service request</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Frequency</div>
                <Skeleton className="h-5 w-28" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Duration (minutes)</div>
                <Skeleton className="h-5 w-16" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Preferred Days</div>
                <Skeleton className="h-5 w-48" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">
                  Preferred Time of Day
                </div>
                <Skeleton className="h-5 w-24" />
              </div>
              <div className="space-y-1 col-span-1 md:col-span-2">
                <div className="text-sm font-medium text-muted-foreground">Additional Notes</div>
                <Skeleton className="h-16 w-full" />
              </div>
            </CardContent>
          </Card>

          {/* Family Availability */}
          <Card>
            <CardHeader>
              <CardTitle>Family Availability</CardTitle>
              <CardDescription>When the family is available for services</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">
                  General Availability
                </div>
                <Skeleton className="h-16 w-full" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Additional Notes</div>
                <Skeleton className="h-16 w-full" />
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>Additional information about this request</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Created By</div>
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Created At</div>
                <Skeleton className="h-5 w-40" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Updated By</div>
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Updated At</div>
                <Skeleton className="h-5 w-40" />
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Organization</div>
                <Skeleton className="h-5 w-36" />
              </div>
            </CardContent>
          </Card>

          {/* Status actions */}
          <div className="mt-6 pt-6 border-t">
            <Skeleton className="h-6 w-20 mb-4" />
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Request history */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-40" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-64" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="flex justify-between">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-4 w-full max-w-md" />
                  <div className="border rounded-md p-3 mt-2">
                    <Skeleton className="h-4 w-full max-w-sm mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
