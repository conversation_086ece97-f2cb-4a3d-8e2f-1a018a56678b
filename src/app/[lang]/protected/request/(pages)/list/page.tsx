import { notFound } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { listRequests } from "../../actions/list";
// import { ActionState } from "@/lib/types/responses";
import { RequestTable } from "../../components/RequestTable";
import { RequestFilters } from "../../components/RequestFilters";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import DOMAIN_CONFIG from "../../lib/config/domain";
import { CreateRequestButton } from "../../components/CreateRequestButton";

interface ListPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    status?: string;
    sort?: string;
    dir?: string;
    from_date?: string;
    to_date?: string;
    assignee_id?: string;
  }>;
}

/**
 * List page for Request items
 * Displays a table of requests with filtering, sorting, and pagination
 */
export default async function ListPage({ params, searchParams }: ListPageProps) {
  // Await the params and searchParams
  const { lang } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const page = searchParamsData.page ? parseInt(searchParamsData.page) : 1;
  const limit = searchParamsData.limit ? parseInt(searchParamsData.limit) : 10;
  const search = searchParamsData.search || "";
  // Handle comma-separated status values
  const status = searchParamsData.status
    ? searchParamsData.status.split(",").filter(Boolean)
    : undefined;
  const sortBy = searchParamsData.sort || "created_at";
  const sortOrder = searchParamsData.dir || "desc";
  const fromDate = searchParamsData.from_date || undefined;
  const toDate = searchParamsData.to_date || undefined;
  const assigneeId = searchParamsData.assignee_id || undefined;

  // Call the server action to get the data with all parameters
  const state = await listRequests({
    page,
    limit,
    search,
    status: status as any,
    sortBy: sortBy as any,
    sortOrder: sortOrder as any,
    from_date: fromDate,
    to_date: toDate,
    assignee_id: assigneeId,
  });

  // Extract items and total count
  const items = state.data?.items || [];
  const totalItems = state.data?.total || 0;
  const totalPages = Math.ceil(totalItems / limit);

  // Create URL for a specific page
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams();

    // Add all current search params
    for (const [key, value] of Object.entries(searchParamsData)) {
      if (key !== "page" && value !== undefined) {
        params.set(key, value.toString());
      }
    }

    // Set the page parameter
    params.set("page", page.toString());

    // Return the full URL
    return `/${lang}${DOMAIN_CONFIG.basePath}/list?${params.toString()}`;
  };

  return (
    <Card>
      <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <CardTitle>{dictionary.request.title}</CardTitle>
          <CardDescription>{dictionary.request.description}</CardDescription>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/${lang}${DOMAIN_CONFIG.basePath}/drafts`}>
              <FileText className="h-4 w-4 mr-2" />
              Drafts
            </Link>
          </Button>
          <AuthorizationRequired permission={DOMAIN_CONFIG.permissions.CREATE}>
            <CreateRequestButton lang={lang} dictionary={dictionary} />
          </AuthorizationRequired>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <RequestFilters lang={lang} dictionary={dictionary} basePath={DOMAIN_CONFIG.basePath} />

        {/* Table */}
        <RequestTable requests={items} lang={lang} dictionary={dictionary} />

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <Pagination>
              <PaginationContent>
                {/* Previous button */}
                <PaginationItem>
                  {page > 1 ? (
                    <PaginationPrevious href={createPageUrl(page - 1)} />
                  ) : (
                    <PaginationPrevious href="#" className="pointer-events-none opacity-50" />
                  )}
                </PaginationItem>

                {/* First page */}
                <PaginationItem>
                  <PaginationLink href={createPageUrl(1)} isActive={page === 1}>
                    1
                  </PaginationLink>
                </PaginationItem>

                {/* Last page if more than 1 page */}
                {totalPages > 1 && (
                  <PaginationItem>
                    <PaginationLink href={createPageUrl(totalPages)} isActive={page === totalPages}>
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Next button */}
                <PaginationItem>
                  {page < totalPages ? (
                    <PaginationNext href={createPageUrl(page + 1)} />
                  ) : (
                    <PaginationNext href="#" className="pointer-events-none opacity-50" />
                  )}
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
