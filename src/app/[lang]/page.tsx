import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ThemeToggle } from "@/components/theme-toggle";
import { LanguageToggle } from "@/components/language-toggle";
import { i18n } from "@/lib/i18n/services/I18nService";
import { H1, H2, P, Lead } from "@/components/typography";
import { ArrowRight, Users, Calendar, FileText, Shield } from "lucide-react";

export default async function Home({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="flex flex-col items-center min-h-screen p-4 max-w-7xl mx-auto">
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <LanguageToggle currentLang={lang} />
        <ThemeToggle />
      </div>

      {/* Hero Section */}
      <div className="w-full py-12 md:py-24 lg:py-32 space-y-8">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center">
            <H1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              {dictionary.common.appName}
            </H1>
            <Lead className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {dictionary.common.welcome}
            </Lead>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Button asChild>
                <Link href={`/${lang}/auth/signin`}>{dictionary.auth.signin.signInButton}</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href={`/${lang}/docs/design-system`}>{dictionary.home.getStarted}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Platform Features
              </H2>
              <P className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
                A comprehensive solution for supervised visitation management
              </P>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 mt-8">
            <div className="lg:col-span-1">
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Users className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Contact Management</CardTitle>
                  <CardDescription>
                    Comprehensive contact management with relationship tracking
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Maintain detailed records of all contacts, including family members,
                    professionals, and organizations. Track relationships between contacts for a
                    complete view of the network.
                  </P>
                </CardContent>
              </Card>
            </div>
            <div className="lg:col-span-1">
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Scheduling System</CardTitle>
                  <CardDescription>Efficient scheduling for supervised visitations</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Schedule and manage supervised visitations with an intuitive calendar interface.
                    Manage room availability, staff assignments, and participant notifications.
                  </P>
                </CardContent>
              </Card>
            </div>
            <div className="lg:col-span-1">
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <FileText className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Case Management</CardTitle>
                  <CardDescription>Comprehensive case tracking and documentation</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Maintain detailed case records, including notes, documents, and history. Track
                    progress and outcomes for each case with customizable workflows.
                  </P>
                </CardContent>
              </Card>
            </div>
            <div className="lg:col-span-1">
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Shield className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Multi-Tenant Security</CardTitle>
                  <CardDescription>Robust security with organization isolation</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Each organization's data is securely isolated with row-level security.
                    Role-based access control ensures users only see what they need to.
                  </P>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Documentation Section */}
      <div className="w-full py-12 md:py-24 lg:py-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Developer Resources
              </H2>
              <P className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
                Comprehensive documentation for developers and AI assistants
              </P>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 mt-8">
            <div>
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-8 w-8 text-primary"
                    >
                      <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2" />
                      <path d="M18 14h-8" />
                      <path d="M15 18h-5" />
                      <path d="M10 6h8v4h-8V6Z" />
                    </svg>
                  </div>
                  <CardTitle className="text-xl">Design System Guide</CardTitle>
                  <CardDescription>
                    Comprehensive guide to the design system for developers and AI assistants
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Explore our design system documentation, including component usage, layout
                    patterns, typography guidelines, and more. This guide helps maintain consistency
                    across the application.
                  </P>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/${lang}/docs/design-system/ai-guide`}>
                      View Design System Guide <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
            <div>
              <Card className="h-full flex flex-col">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-8 w-8 text-primary"
                    >
                      <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                      <path d="M9 18c-4.51 2-5-2-7-2" />
                    </svg>
                  </div>
                  <CardTitle className="text-xl">GitHub Workflow Guide</CardTitle>
                  <CardDescription>
                    Detailed guide to our GitHub workflow methodology
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow text-center">
                  <P>
                    Learn about our hierarchical issue structure, branching strategy, PR workflow,
                    and helper scripts. This guide is especially useful for AI assistants working on
                    the project.
                  </P>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/${lang}/docs/github-workflow`}>
                      View GitHub Workflow Guide <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="w-full py-6 border-t">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row md:gap-6">
            <P className="text-center text-sm text-gray-500 dark:text-gray-400">
              © 2025 Si Simple. All rights reserved.
            </P>
          </div>
        </div>
      </footer>
    </div>
  );
}
